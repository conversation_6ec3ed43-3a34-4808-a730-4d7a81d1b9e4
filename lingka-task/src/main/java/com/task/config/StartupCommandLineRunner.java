package com.task.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.domain.Sequence;
import com.service.SequenceService;
import com.task.constant.App;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class StartupCommandLineRunner implements CommandLineRunner {

	@Value("${spring.filesystem.url}")
	private String fileSystemUrl;

	@Value("${spring.file.location}")
	private String fileLocation;

	@Autowired
	private SequenceService sequenceService;


	public void run(String... args) throws Exception {
		this.loadApplicationValues();
	}
	
	private void loadApplicationValues(){
		this.setAppId();
		App.FILESYSTEM_URL = this.fileSystemUrl;
		App.FILE_LOCATION = this.fileLocation;
	}


	private void setAppId(){
		Sequence sequence = new Sequence();
		this.sequenceService.create(sequence);
		String id = String.valueOf(sequence.getId());
		App.ID = id.substring(id.length() - 4);
	}


	
}