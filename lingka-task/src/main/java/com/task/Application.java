package com.task;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.task","com.service","com.dao"})
@EnableScheduling
@MapperScan(basePackages = "com.dao")
public class Application extends SpringBootServletInitializer {
	public static void main(String[] args)  {
		SpringApplication.run(Application.class, args);
    }
}