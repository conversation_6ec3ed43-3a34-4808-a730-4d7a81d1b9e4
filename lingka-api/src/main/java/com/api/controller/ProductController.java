package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.validator.ProductIngredientConfigValidator;
import com.api.validator.ProductIngredientValidator;
import com.api.validator.ProductOptionValidator;
import com.api.validator.ProductSkuValidator;
import com.api.validator.ProductValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Product;
import com.domain.ProductIngredient;
import com.domain.ProductIngredientConfig;
import com.domain.ProductOption;
import com.domain.ProductSku;
import com.dto.ProductDTO;
import com.query.ProductIngredientQuery;
import com.query.ProductOptionQuery;
import com.query.ProductQuery;
import com.github.pagehelper.PageInfo;
import com.query.ProductSkuQuery;
import com.service.ProductService;

/**
 * 产品控制器
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;

    /**
     * 增加产品
     */
    @RequestMapping(value = "/v1/product/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ProductQuery request) {
        ProductValidator validator = new ProductValidator();
        if (!validator.onShopId(request.getShopId()).onCatalogId(request.getCatalogId()).onName(request.getName()).onProductSkus(request.getProductSkus()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 逐个验证SKU是否合法
        Set<String> productSkuSizeUnitSet = new HashSet<>();
        for (ProductSkuQuery productSkuQuery : request.getProductSkus()) {
            ProductSkuValidator productSkuValidator = new ProductSkuValidator();
            if (!productSkuValidator.onSize(productSkuQuery.getSize()).onUnit(productSkuQuery.getUnit()).onPrice(productSkuQuery.getPrice()).result()) {
                return new Response<>(ERROR, productSkuValidator.getErrorMessage());
            }
            String productSkuSizeUnit = productSkuQuery.getSize() + productSkuQuery.getUnit();
            if (productSkuSizeUnitSet.contains(productSkuSizeUnit)){
                return new Response<>(ERROR, "SKU大小和单位不能重复");
            }
            productSkuSizeUnitSet.add(productSkuSizeUnit);
        }
        // 检查是否传入了产品选项
        if (request.getProductOptions() != null && !request.getProductOptions().isEmpty()) {
            // 校验选项是否合法
            Set<String> productOptionKeySet = new HashSet<>();
            for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
                ProductOptionValidator productOptionValidator = new ProductOptionValidator();
                if (!productOptionValidator.onKey(productOptionQuery.getKey()).onValues(productOptionQuery.getValues()).result()){
                    return new Response<>(ERROR, productOptionValidator.getErrorMessage());
                }
                if (productOptionKeySet.contains(productOptionQuery.getKey())) {
                    return new Response<>(ERROR, "选项键不能重复");
                }
                productOptionKeySet.add(productOptionQuery.getKey());
                Set<String> productOptionValueSet = new HashSet<>();
                for (String value : productOptionQuery.getValues()) {
                    if (productOptionValueSet.contains(value)) {
                        return new Response<>(ERROR, "选项值不能重复");
                    }
                    productOptionValueSet.add(value);
                }
            }
        }
        // 检查是否传了小料
        if (request.getProductIngredients() != null && !request.getProductIngredients().isEmpty()) {
            // 检查小料是否合法
            Set<String> productIngredientNameSet = new HashSet<>();
            for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
                ProductIngredientValidator productIngredientValidator = new ProductIngredientValidator();
                if (!productIngredientValidator.onName(productIngredientQuery.getName()).onPrice(productIngredientQuery.getPrice()).result()){
                    return new Response<>(ERROR, productIngredientValidator.getErrorMessage());
                }
                if (productIngredientNameSet.contains(productIngredientQuery.getName())) {
                    return new Response<>(ERROR, "小料名称不能重复");
                }
                productIngredientNameSet.add(productIngredientQuery.getName());
            }
            // 检查小料配置是否合法
            if (!validator.onProductIngredientConfig(request.getProductIngredientConfig()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            ProductIngredientConfigValidator productIngredientValidator = new ProductIngredientConfigValidator();
            if (!productIngredientValidator.onRequired(request.getProductIngredientConfig().getRequired()).onType(request.getProductIngredientConfig().getType()).result()) {
                return new Response<>(ERROR, productIngredientValidator.getErrorMessage());
            }
        }
        // 查询这个产品名字是否已经存在
        ProductQuery productQuery = new ProductQuery();
        productQuery.setShopId(request.getShopId());
        productQuery.setName(request.getName());
        productQuery.setStatus(DataStatus.Y.getCode());
        List<ProductDTO> productDTOs = productService.findAll(productQuery);
        if (productDTOs != null && !productDTOs.isEmpty()) {
            return new Response<>(ERROR, "该店铺下已存在该产品名称");
        }
        Product product = new Product();
        BeanUtils.copyProperties(request, product);
        // 构建产品SKU对象
        List<ProductSku> productSkus = new ArrayList<>();
        for (ProductSkuQuery productSkuQuery : request.getProductSkus()) {
            ProductSku productSku = new ProductSku();
            productSku.setSize(productSkuQuery.getSize());
            productSku.setUnit(productSkuQuery.getUnit());
            productSku.setPrice(productSkuQuery.getPrice());
            productSkus.add(productSku);
        }
        // 如果传入的没有小料，没有规格组
        if ((request.getProductIngredients() == null || request.getProductIngredients().isEmpty()) && (request.getProductOptions() == null || request.getProductOptions().isEmpty())) {
            productService.createAndSku(product, productSkus);
            return new Response<>(OK, SUCCESS);
        }
        // 如果只有小料，没有规则组
        if ((request.getProductOptions() == null || request.getProductOptions().isEmpty()) && (request.getProductIngredients() != null && !request.getProductIngredients().isEmpty())) {
            List<ProductIngredient> productIngredients = new ArrayList<>();
            for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
                ProductIngredient productIngredient = new ProductIngredient();
                productIngredient.setName(productIngredientQuery.getName());
                productIngredient.setPrice(productIngredientQuery.getPrice());
                productIngredients.add(productIngredient);
            }
            // 创建产品小料配置
            ProductIngredientConfig productIngredientConfig = new ProductIngredientConfig();
            productIngredientConfig.setType(request.getProductIngredientConfig().getType());
            productIngredientConfig.setRequired(request.getProductIngredientConfig().getRequired());
            productService.createAndSkuAndIngredientAndConfig(product, productSkus, productIngredients, productIngredientConfig);
            return new Response<>(OK, SUCCESS);
        }
        // 如果没有小料，只有有规则组
        if ((request.getProductIngredients() == null || request.getProductIngredients().isEmpty()) && (request.getProductOptions() != null && !request.getProductOptions().isEmpty())) {
            // 创建规则组
            List<ProductOption> productOptions = new ArrayList<>();
            for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
                for (String value : productOptionQuery.getValues()) {
                    ProductOption productOption = new ProductOption();
                    productOption.setKey(productOptionQuery.getKey());
                    productOption.setValue(value);
                    productOptions.add(productOption);
                }
            }
            productService.createAndSkuAndOption(product, productSkus, productOptions);
            return new Response<>(OK, SUCCESS);
        }
        // 小料和规则组都有
        // 创建小料
        List<ProductIngredient> productIngredients = new ArrayList<>();
        for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
            ProductIngredient productIngredient = new ProductIngredient();
            productIngredient.setName(productIngredientQuery.getName());
            productIngredient.setPrice(productIngredientQuery.getPrice());
            productIngredients.add(productIngredient);
        }
        // 创建产品小料配置
        ProductIngredientConfig productIngredientConfig = new ProductIngredientConfig();
        productIngredientConfig.setType(request.getProductIngredientConfig().getType());
        productIngredientConfig.setRequired(request.getProductIngredientConfig().getRequired());
        // 创建规则组
        List<ProductOption> productOptions = new ArrayList<>();
        for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
            for (String value : productOptionQuery.getValues()) {
                ProductOption productOption = new ProductOption();
                productOption.setKey(productOptionQuery.getKey());
                productOption.setValue(value);
                productOptions.add(productOption);
            }
        }
        productService.createAndSkuAndIngredientAndConfigAndOption(product, productSkus, productOptions,productIngredients, productIngredientConfig);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询产品列表
     */
    @RequestMapping(value = "/v1/product/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductDTO>> query(@RequestBody ProductQuery productQuery) {
        productQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductDTO> pageInfo = productService.find(productQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询产品列表
     */
    @RequestMapping(value = "/v1/product/all/query", method = RequestMethod.POST)
    public Response<List<ProductDTO>> queryAll(@RequestBody ProductQuery productQuery) {
        productQuery.setStatus(DataStatus.Y.getCode());
        List<ProductDTO> productDTOs = productService.findAll(productQuery);
        return new Response<>(productDTOs);
    }


    /**
     * 修改产品
     */
    @RequestMapping(value = "/v1/product/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ProductQuery request) {
        ProductValidator validator = new ProductValidator();
        if (!validator.onId(request.getId()).onShopId(request.getShopId()).onCatalogId(request.getCatalogId()).onName(request.getName()).onProductSkus(request.getProductSkus()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 逐个验证SKU是否合法
        Set<String> productSkuSizeUnitSet = new HashSet<>();
        for (ProductSkuQuery productSkuQuery : request.getProductSkus()) {
            ProductSkuValidator productSkuValidator = new ProductSkuValidator();
            if (!productSkuValidator.onSize(productSkuQuery.getSize()).onUnit(productSkuQuery.getUnit()).onPrice(productSkuQuery.getPrice()).result()) {
                return new Response<>(ERROR, productSkuValidator.getErrorMessage());
            }
            String productSkuSizeUnit = productSkuQuery.getSize() + productSkuQuery.getUnit();
            if (productSkuSizeUnitSet.contains(productSkuSizeUnit)){
                return new Response<>(ERROR, "SKU大小和单位不能重复");
            }
            productSkuSizeUnitSet.add(productSkuSizeUnit);
        }
        // 检查是否传入了产品选项
        if (request.getProductOptions() != null && !request.getProductOptions().isEmpty()) {
            // 校验选项是否合法
            Set<String> productOptionKeySet = new HashSet<>();
            for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
                ProductOptionValidator productOptionValidator = new ProductOptionValidator();
                if (!productOptionValidator.onKey(productOptionQuery.getKey()).onValues(productOptionQuery.getValues()).result()){
                    return new Response<>(ERROR, productOptionValidator.getErrorMessage());
                }
                if (productOptionKeySet.contains(productOptionQuery.getKey())) {
                    return new Response<>(ERROR, "选项键不能重复");
                }
                productOptionKeySet.add(productOptionQuery.getKey());
                Set<String> productOptionValueSet = new HashSet<>();
                for (String value : productOptionQuery.getValues()) {
                    if (productOptionValueSet.contains(value)) {
                        return new Response<>(ERROR, "选项值不能重复");
                    }
                    productOptionValueSet.add(value);
                }
            }
        }
        // 检查是否传了小料
        if (request.getProductIngredients() != null && !request.getProductIngredients().isEmpty()) {
            // 检查小料是否合法
            Set<String> productIngredientNameSet = new HashSet<>();
            for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
                ProductIngredientValidator productIngredientValidator = new ProductIngredientValidator();
                if (!productIngredientValidator.onName(productIngredientQuery.getName()).onPrice(productIngredientQuery.getPrice()).result()){
                    return new Response<>(ERROR, productIngredientValidator.getErrorMessage());
                }
                if (productIngredientNameSet.contains(productIngredientQuery.getName())) {
                    return new Response<>(ERROR, "小料名称不能重复");
                }
                productIngredientNameSet.add(productIngredientQuery.getName());
            }
            // 检查小料配置是否合法
            if (!validator.onProductIngredientConfig(request.getProductIngredientConfig()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            ProductIngredientConfigValidator productIngredientValidator = new ProductIngredientConfigValidator();
            if (!productIngredientValidator.onRequired(request.getProductIngredientConfig().getRequired()).onType(request.getProductIngredientConfig().getType()).result()) {
                return new Response<>(ERROR, productIngredientValidator.getErrorMessage());
            }
        }
        // 查询这个产品名字是否已经存在
        ProductQuery productQuery = new ProductQuery();
        productQuery.setShopId(request.getShopId());
        productQuery.setName(request.getName());
        productQuery.setStatus(DataStatus.Y.getCode());
        List<ProductDTO> productDTOs = productService.findAll(productQuery);
        if (productDTOs != null && !productDTOs.isEmpty() && !productDTOs.get(0).getId().equals(request.getId())) {
            return new Response<>(ERROR, "该店铺下已存在该产品名称");
        }
        Product product = new Product();
        BeanUtils.copyProperties(request, product);
        // 构建产品SKU对象
        List<ProductSku> productSkus = new ArrayList<>();
        for (ProductSkuQuery productSkuQuery : request.getProductSkus()) {
            ProductSku productSku = new ProductSku();
            productSku.setId(productSkuQuery.getId());
            productSku.setSize(productSkuQuery.getSize());
            productSku.setUnit(productSkuQuery.getUnit());
            productSku.setPrice(productSkuQuery.getPrice());
            productSkus.add(productSku);
        }
        // 如果传入的没有小料，没有规格组
        if ((request.getProductIngredients() == null || request.getProductIngredients().isEmpty()) && (request.getProductOptions() == null || request.getProductOptions().isEmpty())) {
            productService.modifyAndSku(product, productSkus);
            return new Response<>(OK, SUCCESS);
        }
        // 如果只有小料，没有规则组
        if ((request.getProductOptions() == null || request.getProductOptions().isEmpty()) && (request.getProductIngredients() != null && !request.getProductIngredients().isEmpty())) {
            List<ProductIngredient> productIngredients = new ArrayList<>();
            for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
                ProductIngredient productIngredient = new ProductIngredient();
                productIngredient.setId(productIngredientQuery.getId());
                productIngredient.setName(productIngredientQuery.getName());
                productIngredient.setPrice(productIngredientQuery.getPrice());
                productIngredients.add(productIngredient);
            }
            // 创建产品小料配置
            ProductIngredientConfig productIngredientConfig = new ProductIngredientConfig();
            productIngredientConfig.setId(request.getProductIngredientConfig().getId());
            productIngredientConfig.setType(request.getProductIngredientConfig().getType());
            productIngredientConfig.setRequired(request.getProductIngredientConfig().getRequired());
            productService.modifyAndSkuAndIngredientAndConfig(product, productSkus, productIngredients, productIngredientConfig);
            return new Response<>(OK, SUCCESS);
        }
        // 如果只有规则组，没有小料
        if ((request.getProductIngredients() == null || request.getProductIngredients().isEmpty()) && (request.getProductOptions() != null && !request.getProductOptions().isEmpty())) {
            List<ProductOption> productOptions = new ArrayList<>();
            for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
                for (String value : productOptionQuery.getValues()) {
                    ProductOption productOption = new ProductOption();
                    productOption.setId(productOptionQuery.getId());
                    productOption.setKey(productOptionQuery.getKey());
                    productOption.setValue(value);
                    productOptions.add(productOption);
                }
            }
            productService.modifyAndSkuAndOption(product, productSkus, productOptions);
            return new Response<>(OK, SUCCESS);
        }
        // 小料和规则组都有
        List<ProductIngredient> productIngredients = new ArrayList<>();
        for (ProductIngredientQuery productIngredientQuery : request.getProductIngredients()) {
            ProductIngredient productIngredient = new ProductIngredient();
            productIngredient.setId(productIngredientQuery.getId());
            productIngredient.setName(productIngredientQuery.getName());
            productIngredient.setPrice(productIngredientQuery.getPrice());
            productIngredients.add(productIngredient);
        }
        // 创建规则组
        List<ProductOption> productOptions = new ArrayList<>();
        for (ProductOptionQuery productOptionQuery : request.getProductOptions()) {
            for (String value : productOptionQuery.getValues()) {
                ProductOption productOption = new ProductOption();
                productOption.setId(productOptionQuery.getId());
                productOption.setKey(productOptionQuery.getKey());
                productOption.setValue(value);
                productOptions.add(productOption);
            }
        }
        // 创建产品小料配置
        ProductIngredientConfig productIngredientConfig = new ProductIngredientConfig();
        productIngredientConfig.setId(request.getProductIngredientConfig().getId());
        productIngredientConfig.setType(request.getProductIngredientConfig().getType());
        productIngredientConfig.setRequired(request.getProductIngredientConfig().getRequired());
        productService.modifyAndSkuAndIngredientAndConfigAndOption(product, productSkus, productOptions, productIngredients, productIngredientConfig);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除产品
     */
    @RequestMapping(value = "/v1/product/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProductQuery request) {
        Date datetime = this.getServerTime();
        Product product = new Product();
        product.setId(request.getId());
        product.setStatus(DataStatus.N.getCode());
        product.setModifyTime(datetime);
        productService.modifyById(product);
        return new Response<>(OK, SUCCESS);
    }


}
