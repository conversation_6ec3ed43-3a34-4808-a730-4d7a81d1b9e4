package com.api.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.CodeNameDescriptionResponse;
import com.common.bean.NameValuePairResponse;
import com.common.bean.Response;
import com.common.constant.Gender;
import com.common.constant.OrderMode;
import com.common.constant.ShopState;
import com.common.constant.TagCatalog;

/**
 * 字典模块接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
public class DictionaryController extends BaseController {

    /**
     * 查询性别
     */
    @RequestMapping(value = "/v1/dictionary/gender/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryGender() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (Gender value : Gender.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }


    /**
     * 查询标签分类枚举
     *
     */
    @RequestMapping(value = "/v1/dictionary/tag/catalog/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryTagCatalog() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (TagCatalog value : TagCatalog.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getName());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询点单制
     *
     */
    @RequestMapping(value = "/v1/dictionary/order/mode/query", method = {RequestMethod.POST})
    public Response<List<CodeNameDescriptionResponse>> queryOrderMode() {
        List<CodeNameDescriptionResponse> response = new ArrayList<>();
        for (OrderMode value : OrderMode.values()) {
            CodeNameDescriptionResponse codeNameDescriptionResponse = new CodeNameDescriptionResponse();
            codeNameDescriptionResponse.setCode(value.getCode());
            codeNameDescriptionResponse.setName(value.getName());
            codeNameDescriptionResponse.setDescription(value.getDescription());
            response.add(codeNameDescriptionResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

    /**
     * 查询营业状态
     *
     */
    @RequestMapping(value = "/v1/dictionary/shop/state/query", method = {RequestMethod.POST})
    public Response<List<NameValuePairResponse>> queryShopState() {
        List<NameValuePairResponse> response = new ArrayList<>();
        for (ShopState value : ShopState.values()) {
            NameValuePairResponse nameValuePairResponse = new NameValuePairResponse();
            nameValuePairResponse.setCode(value.getCode());
            nameValuePairResponse.setName(value.getDescription());
            response.add(nameValuePairResponse);
        }
        return new Response<>(OK, SUCCESS, response);
    }

}
