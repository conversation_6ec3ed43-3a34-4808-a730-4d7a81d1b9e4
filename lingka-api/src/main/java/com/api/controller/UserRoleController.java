package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.UserRole;
import com.dto.UserRoleDTO;
import com.query.UserRoleQuery;
import com.github.pagehelper.PageInfo;
import com.service.UserRoleService;

/**
 * 用户角色关联控制器
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@RestController
public class UserRoleController extends BaseController {

    @Autowired
    private UserRoleService userRoleService;

    /**
     * 增加用户角色关联
     */
    @RequestMapping(value = "/v1/user/role/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody UserRole userRole) {
        Date datetime = this.getServerTime();
        userRole.setStatus(DataStatus.Y.getCode());
        userRole.setModifyTime(datetime);
        userRole.setCreateTime(datetime);
        userRoleService.create(userRole);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询用户角色关联列表
     */
    @RequestMapping(value = "/v1/user/role/query", method = RequestMethod.POST)
    public Response<PageInfo<UserRoleDTO>> query(@RequestBody UserRoleQuery userRoleQuery) {
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserRoleDTO> pageInfo = userRoleService.find(userRoleQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询用户角色关联列表
     */
    @RequestMapping(value = "/v1/user/role/all/query", method = RequestMethod.POST)
    public Response<List<UserRoleDTO>> queryAll(@RequestBody UserRoleQuery userRoleQuery) {
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOs = userRoleService.findAll(userRoleQuery);
        return new Response<>(userRoleDTOs);
    }

    /**
     * 查询我在某个店铺下的用户角色IDS
     */
    @RequestMapping(value = "/v1/user/role/all/my/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<List<Long>> queryMyAll(@RequestBody UserRoleQuery userRoleQuery) {
        if (isEmpty(userRoleQuery.getShopId())) {
            return new Response<>(new ArrayList<>());
        }
        userRoleQuery.setUserId(this.getUserToken().getId());
        userRoleQuery.setShopId(userRoleQuery.getShopId());
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOs = userRoleService.findAll(userRoleQuery);
        List<Long> roleIds = new ArrayList<>();
        for (UserRoleDTO userRoleDTO : userRoleDTOs) {
            roleIds.add(userRoleDTO.getRoleId());
        }
        return new Response<>(roleIds);
    }


    /**
     * 修改用户角色关联
     */
    @RequestMapping(value = "/v1/user/role/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody UserRole userRole) {
        Date datetime = this.getServerTime();
        userRole.setModifyTime(datetime);
        userRoleService.modifyById(userRole);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除用户角色关联
     */
    @RequestMapping(value = "/v1/user/role/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody UserRoleQuery request) {
        Date datetime = this.getServerTime();
        UserRole userRole = new UserRole();
        userRole.setId(request.getId());
        userRole.setStatus(DataStatus.N.getCode());
        userRole.setModifyTime(datetime);
        userRoleService.modifyById(userRole);
        return new Response<>(OK, SUCCESS);
    }


}
