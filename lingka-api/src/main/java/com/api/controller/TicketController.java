package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Ticket;
import com.domain.TicketProduct;
import com.dto.TicketDTO;
import com.query.TicketProductQuery;
import com.query.TicketQuery;
import com.github.pagehelper.PageInfo;
import com.service.TicketService;

/**
 * 门票控制器
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@RestController
public class TicketController extends BaseController {

    @Autowired
    private TicketService ticketService;

    /**
     * 增加门票
     */
    @RequestMapping(value = "/v1/ticket/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody TicketQuery request) {
        Ticket ticket = new Ticket();
        Date datetime = this.getServerTime();
        BeanUtils.copyProperties(request,ticket);
        List<TicketProduct> ticketProducts = new ArrayList<>();
        if (request.getTicketProducts() != null && !request.getTicketProducts().isEmpty()){
            for (TicketProductQuery ticketProductQuery : request.getTicketProducts()) {
                TicketProduct ticketProductCreate = new TicketProduct();
                ticketProductCreate.setProductId(ticketProductQuery.getProductId());
                ticketProductCreate.setProductNumber(ticketProductQuery.getProductNumber());
                ticketProductCreate.setStatus(DataStatus.Y.getCode());
                ticketProductCreate.setModifyTime(datetime);
                ticketProductCreate.setCreateTime(datetime);
                ticketProducts.add(ticketProductCreate);
            }
        }
        ticket.setStatus(DataStatus.Y.getCode());
        ticket.setModifyTime(datetime);
        ticket.setCreateTime(datetime);
        ticketService.create(ticket,ticketProducts);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询门票列表
     */
    @RequestMapping(value = "/v1/ticket/query", method = RequestMethod.POST)
    public Response<PageInfo<TicketDTO>> query(@RequestBody TicketQuery ticketQuery) {
        ticketQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<TicketDTO> pageInfo = ticketService.find(ticketQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询门票列表
     */
    @RequestMapping(value = "/v1/ticket/all/query", method = RequestMethod.POST)
    public Response<List<TicketDTO>> queryAll(@RequestBody TicketQuery ticketQuery) {
        ticketQuery.setStatus(DataStatus.Y.getCode());
        List<TicketDTO> ticketDTOs = ticketService.findAll(ticketQuery);
        return new Response<>(ticketDTOs);
    }


    /**
     * 修改门票
     */
    @RequestMapping(value = "/v1/ticket/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody TicketQuery request) {
        Date datetime = this.getServerTime();
//        ticket.setModifyTime(datetime);
//        ticketService.modifyById(ticket);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除门票
     */
    @RequestMapping(value = "/v1/ticket/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody TicketQuery request) {
        Date datetime = this.getServerTime();
        Ticket ticket = new Ticket();
        ticket.setId(request.getId());
        ticket.setStatus(DataStatus.N.getCode());
        ticket.setModifyTime(datetime);
        ticketService.modifyById(ticket);
        return new Response<>(OK, SUCCESS);
    }


}
