package com.api.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.UserValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.bean.WeChatMsgSecCheckRequest;
import com.common.bean.WeChatMsgSecCheckResponse;
import com.common.bean.WechatLoginResponse;
import com.common.bean.WechatUserPhoneNumberRequest;
import com.common.bean.WechatUserPhoneNumberResponse;
import com.common.client.WechatClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.Device;
import com.common.constant.MsgCheckScene;
import com.common.constant.MsgCheckSuggest;
import com.common.constant.Platform;
import com.common.constant.TokenType;
import com.common.util.DateUtil;
import com.common.util.EncryptUtil;
import com.common.util.ReflectDiffUtils;
import com.domain.User;
import com.dto.UserDTO;
import com.dto.WechatAppDTO;
import com.github.pagehelper.PageInfo;
import com.query.UserQuery;
import com.service.UserService;
import com.service.WechatAppService;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
public class UserController extends BaseController {

    @Autowired
    private UserService userService;
    @Autowired
    private WechatAppService wechatAppService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 增加用户
     */
    @RequestMapping(value = "/v1/user/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody User user) {
        Date datetime = this.getServerTime();
        user.setStatus(DataStatus.Y.getCode());
        user.setModifyTime(datetime);
        user.setCreateTime(datetime);
        userService.create(user);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 微信小程序登录
     *
     */
    @RequestMapping(value = "/v1/user/wechat/login", method = RequestMethod.POST)
    public Response<?> login(@RequestBody UserQuery request) {
        if (isEmpty(request.getLoginCode())) {
            return new Response<>(ERROR, "请传入小程序登录授权的code");
        }
        WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
        if (wechatAppDTO == null) {
            return new Response<>(ERROR, "暂时不支持微信小程序授权登录,请联系管理员");
        }
        StringBuilder loginBuilder = new StringBuilder("/sns/jscode2session?");
        loginBuilder.append("appid=");
        loginBuilder.append(wechatAppDTO.getAppid());
        loginBuilder.append("&secret=");
        loginBuilder.append(wechatAppDTO.getSecret());
        loginBuilder.append("&js_code=");
        loginBuilder.append(request.getLoginCode());
        loginBuilder.append("&grant_type=authorization_code");
        WechatClient<WechatLoginResponse> client = new WechatClient<>();
        client.setMethod(HttpMethod.GET);
        WechatLoginResponse wechatLoginResponse = client.execute(loginBuilder.toString(), null, WechatLoginResponse.class);
        if (wechatLoginResponse.getErrcode() != null && wechatLoginResponse.getErrcode() != 0) {
            return new Response<>(ERROR, "微信授权小程序失败");
        }
        String openid = wechatLoginResponse.getOpenid();
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_USER_LOGIN, openid);
        try {
            if (lock.tryLock(1, TimeUnit.SECONDS)) {
                UserDTO userDTO = userService.findByOpenid(openid);
                if (userDTO == null || isEmpty(userDTO.getOpenid())) {
                    User userCreate = new User();
                    userCreate.setOpenid(openid);
                    int count = userService.create(userCreate);
                    if (count > 0) {
                        userDTO = new UserDTO();
                        userDTO.setId(userCreate.getId());
                    }
                }
                if (userDTO != null) {
                    String token = this.getUserToken(userDTO);
                    userDTO.setToken(token);
                    return new Response<>(userDTO);
                }
            } else {
                return new Response<>(ERROR, "请勿点击过快");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return new Response<>(ERROR, FAILURE);
    }


    /**
     * 微信小程序注册
     *
     */
    @RequestMapping(value = "/v1/user/wechat/register", method = RequestMethod.POST)
    public Response<UserDTO> register(@RequestBody UserQuery request) {
        if (isEmpty(request.getLoginCode())) {
            return new Response<>(ERROR, "请传入小程序登录授权的code");
        }
        WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
        if (wechatAppDTO == null) {
            return new Response<>(ERROR, "暂时不支持微信小程序授权登录,请联系管理员");
        }
        StringBuilder loginBuilder = new StringBuilder("/sns/jscode2session?");
        loginBuilder.append("appid=");
        loginBuilder.append(wechatAppDTO.getAppid());
        loginBuilder.append("&secret=");
        loginBuilder.append(wechatAppDTO.getSecret());
        loginBuilder.append("&js_code=");
        loginBuilder.append(request.getLoginCode());
        loginBuilder.append("&grant_type=authorization_code");
        WechatClient<WechatLoginResponse> client = new WechatClient<>();
        client.setMethod(HttpMethod.GET);
        WechatLoginResponse wechatLoginResponse = client.execute(loginBuilder.toString(), null, WechatLoginResponse.class);
        if (wechatLoginResponse.getErrcode() != null && wechatLoginResponse.getErrcode() != 0) {
            return new Response<>(ERROR, "微信授权小程序失败");
        }
        String openid = wechatLoginResponse.getOpenid();
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_USER_LOGIN, openid);
        try {
            if (lock.tryLock(1, TimeUnit.SECONDS)) {
                UserDTO userDTO = userService.findByOpenid(openid);
                if (userDTO != null) {
                    return new Response<>(ERROR, "该用户已经注册，请误重复注册");
                }
                User userCreate = new User();
                userCreate.setOpenid(openid);
                // userCreate.setMobile(mobile);
                userCreate.setGender(request.getGender());
                userCreate.setNickname(request.getNickname());
                userCreate.setAvatar(request.getAvatar());
                userService.create(userCreate);
                UserDTO userDTO1 = new UserDTO();
                userDTO1.setId(userCreate.getId());
                String token = this.getUserToken(userDTO1);
                userDTO1.setToken(token);
                return new Response<>(userDTO1);
            } else {
                return new Response<>(ERROR, "请勿点击过快");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 用户绑定手机号
     */
    @RequestMapping(value = "/v1/user/mobile/bind", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> bindMobile(@RequestBody UserQuery request) {
        UserValidator validator = new UserValidator();
        if (!validator.onMobileCode(request.getMobileCode()).result()) {
            return new Response<>(validator.getErrorMessage());
        }
        Long userId = this.getUserToken().getId();
        Lock lock = getLock(redisTemplate, CacheKey.LOCK_USER_MOBILE_BIND, userId.toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复授权获取用户手机号");
        }
        try {
            WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
            if (wechatAppDTO == null) {
                return new Response<>(ERROR, "暂时不支持微信小程序授权登录,请联系管理员");
            }
            UserDTO userDTO = userService.findById(userId);
            if (userDTO.getMobile() != null) {
                return new Response<>(ERROR, "您已经绑定过手机号，请勿重复绑定");
            }
            StringBuilder mobileBuilder = new StringBuilder("/wxa/business/getuserphonenumber?");
            mobileBuilder.append("access_token=");
            mobileBuilder.append(wechatAppDTO.getAccessToken());
            WechatClient<WechatUserPhoneNumberResponse> getMobileClient = new WechatClient<>();
            getMobileClient.setMethod(HttpMethod.POST);
            WechatUserPhoneNumberRequest wechatUserPhoneNumberRequest = new WechatUserPhoneNumberRequest();
            wechatUserPhoneNumberRequest.setCode(request.getMobileCode());
            WechatUserPhoneNumberResponse wechatUserPhoneNumberResponse = getMobileClient.execute(mobileBuilder.toString(), wechatUserPhoneNumberRequest, WechatUserPhoneNumberResponse.class);
            if (wechatUserPhoneNumberResponse.getErrcode() != null && wechatUserPhoneNumberResponse.getErrcode() != 0) {
                return new Response<>(ERROR, "微信手机号验证失败");
            }
            String mobile = wechatUserPhoneNumberResponse.getPhoneInfo().getPurePhoneNumber();
            // 查询该手机号是否已经注册
            UserDTO mobileUserDTO = userService.findByMobile(mobile);
            if (mobileUserDTO != null) {
                return new Response<>(ERROR, "该微信号绑定的手机号已经注册，无法再次注册");
            }
            // 修改为用户手机号
            User user = new User();
            user.setId(userDTO.getId());
            user.setMobile(mobile);
            user.setModifyTime(this.getServerTime());
            userService.modifyById(user);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
        return new Response<>();
    }

    /**
     * 查询我的个人信息
     */
    @RequestMapping(value = "/v1/user/my/query",method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<UserDTO> queryMy() {
        Long userId = this.getUserToken().getId();
        UserDTO userDTO = userService.findById(userId);
        return DataStatus.Y.getCode().equals(userDTO.getStatus()) ? new Response<>(OK, SUCCESS, userDTO) : new Response<>(ERROR, FAILURE + "：用户不存在或已被禁用");
    }

    /**
     * 分页查询用户列表
     */
    @RequestMapping(value = "/v1/user/query", method = RequestMethod.POST)
    public Response<PageInfo<UserDTO>> query(@RequestBody UserQuery userQuery) {
        userQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserDTO> pageInfo = userService.find(userQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询用户列表
     */
    @RequestMapping(value = "/v1/user/all/query", method = RequestMethod.POST)
    public Response<List<UserDTO>> queryAll(@RequestBody UserQuery userQuery) {
        userQuery.setStatus(DataStatus.Y.getCode());
        List<UserDTO> userDTOs = userService.findAll(userQuery);
        return new Response<>(userDTOs);
    }


    /**
     * 用户修改个人信息
     */
    @Token(tokenType = TokenType.C)
    @RequestMapping(value = "/v1/user/my/modify", method = RequestMethod.POST)
    public Response<?> modifyMy(@RequestBody UserQuery userQuery) {
        Long userId = this.getUserToken().getId();
        userQuery.setId(userId);
        UserDTO userDTOOld = userService.findById(userId);
        String openID = userDTOOld.getOpenid();
        User userOld = new User(userDTOOld);
        Date datetime = this.getServerTime();
        userQuery.setCreateTime(userOld.getCreateTime());
        userQuery.setModifyTime(datetime);
        User userNew = new User(userQuery);
        Map<String, Object> changes = ReflectDiffUtils.diff(userOld, userNew);
        Map<String, Object> params = new HashMap<>();
        params.put("id", userId);
        boolean isPass = false;
        if (changes.containsKey("introduce")) {
            WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
            if (wechatAppDTO == null) {
                return new Response<>(ERROR, FAILURE + "：微信小程序未配置,请联系管理员");
            }
            String accessToken = wechatAppDTO.getAccessToken();

            String msgCheckUrl = "/wxa/msg_sec_check?access_token=" + accessToken;
            WechatClient<WeChatMsgSecCheckResponse> checkResponseWechatClient = new WechatClient<>();
            checkResponseWechatClient.setMethod(HttpMethod.POST);
            WeChatMsgSecCheckRequest request = new WeChatMsgSecCheckRequest();
            request.setScene(MsgCheckScene.PROFILE.getCode());
            request.setOpenid(openID);
            String msgCheckContent = ((String) changes.get("introduce")).trim();
            request.setContent(msgCheckContent);
            WeChatMsgSecCheckResponse response = checkResponseWechatClient.execute(msgCheckUrl, request, WeChatMsgSecCheckResponse.class);
            if (response != null && response.getErrcode() == 0) {
                isPass = MsgCheckSuggest.pass.getCode().equals(response.getResult().getSuggest());
            }
        } else {
            isPass = true;
        }
        if (!isPass) {
            changes.remove("introduce");
        }
        params.put("changes", changes);
        userService.updatePartialById(params);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除用户
     */
    @RequestMapping(value = "/v1/user/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody UserQuery request) {
        Date datetime = this.getServerTime();
        User user = new User();
        user.setId(request.getId());
        user.setStatus(DataStatus.N.getCode());
        user.setModifyTime(datetime);
        userService.modifyById(user);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 获取用户凭证
     */
    private String getUserToken(UserDTO userDTO) {
        Date date = this.getServerTime();
        JsonWebToken jsonWebToken = new JsonWebToken();
        jsonWebToken.setId(UUID.randomUUID().toString());
        jsonWebToken.setIssuedAt(date);
        jsonWebToken.setExpiration(DateUtil.add(date, com.api.constant.App.TOKEN_EXPIRE_UNIT, com.api.constant.App.TOKEN_EXPIRE_NUMBER));
        jsonWebToken.setIssuer(String.valueOf(userDTO.getId()));
        // 用户信息
        jsonWebToken.setUserId(userDTO.getId());
        jsonWebToken.setType(TokenType.C.name());
        String token = EncryptUtil.createJwt(jsonWebToken);
        Device device = Platform.DEVIVCE.get(this.httpServletRequest.getHeader(App.HTTP_HEADER_APP_PLATFORM));
        this.redisTemplate.opsForValue().set(CacheKey.USER_TOKEN + TokenType.C.name() + device.name() + userDTO.getId(), token);
        return token;
    }

}
