package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.api.config.Token;
import com.api.validator.ShopValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.Shop;
import com.domain.ShopConfig;
import com.dto.AdminDTO;
import com.dto.ShopConfigDTO;
import com.dto.ShopDTO;
import com.dto.ShopDetailDTO;
import com.github.pagehelper.PageInfo;
import com.query.ShopQuery;
import com.service.AdminService;
import com.service.ShopConfigService;
import com.service.ShopService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 店铺管理模块接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/shop")
public class ShopController extends BaseController {
    @Autowired
    private ShopService shopService;
    @Autowired
    private AdminService adminService;
    @Autowired
    private ShopConfigService shopConfigService;

    /**
     * 查询店铺列表
     */
    @PostMapping("/list")
    @Token(tokenType = {TokenType.S, TokenType.C})
    @ResponseBody
    public Response<PageInfo<ShopDTO>> list(@RequestBody ShopQuery shopQuery) {
        shopQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ShopDTO> pageInfo = shopService.selectShopList(shopQuery);
        Set<Long> shopIds = new HashSet<>();
        Set<Long> adminIds = new HashSet<>();
        for (ShopDTO shopDTO : pageInfo.getList()) {
            shopIds.add(shopDTO.getId());
            adminIds.add(shopDTO.getCreateAdminId());
        }
        Map<Long, AdminDTO> adminDTOMap = adminService.findMapByIds(new ArrayList<>(adminIds));
        Map<Long, ShopConfigDTO> shopConfigDTOMap = shopConfigService.findMapByShopIds(new ArrayList<>(shopIds));
        for (ShopDTO shopDTO : pageInfo.getList()) {
            if (adminDTOMap.containsKey(shopDTO.getCreateAdminId())) {
                shopDTO.setCreateAdminName(adminDTOMap.get(shopDTO.getCreateAdminId()).getName());
            }
            if (shopConfigDTOMap.containsKey(shopDTO.getId())) {
                shopDTO.setShopName(shopConfigDTOMap.get(shopDTO.getId()).getName());
            }
        }
        return new Response<>(pageInfo);
    }

    /**
     * 查询店铺详情
     */
    @PostMapping("/detail")
    @Token(tokenType = {TokenType.S, TokenType.C})
    @ResponseBody
    public Response<ShopDetailDTO> getDetail(@RequestBody ShopQuery shopQuery) {
        JsonWebToken token = this.getRawJsonWebToken();
        assert token != null;
        Long userId = token.getUserId();
        shopQuery.setStatus(DataStatus.Y.getCode());
        ShopDTO shopDTO = shopService.selectShopById(shopQuery.getId());
        if (shopDTO == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在或已被删除");
        }
        AdminDTO adminDTO = adminService.findById(shopDTO.getCreateAdminId());
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(shopQuery.getId());
        if (adminDTO == null || shopConfigDTO == null) {
            if (adminDTO == null || Objects.equals(DataStatus.N.getCode(), adminDTO.getStatus())) {
                logger.error("查询店铺详情失败：Admin 不存在或已禁用，店铺ID：{}", shopQuery.getId());
            }
            if (shopConfigDTO == null) {
                logger.error("查询店铺详情失败：ShopConfig 不存在，店铺ID：{}", shopQuery.getId());
            }
            return new Response<>(ERROR, FAILURE + "：店铺不存在或已被删除");
        }
        shopDTO.setCreateAdminName(adminDTO.getName());
        shopDTO.setShopName(shopConfigDTO.getName());
        ShopDetailDTO shopDetailDTO = new ShopDetailDTO(shopDTO, shopConfigDTO);
        if (!Objects.equals(userId, shopDetailDTO.getOwnerUserId()) && !TokenType.S.name().equals(token.getType())) {
            shopDetailDTO.setOwnerIdNumber(null);
            shopDetailDTO.setOwnerIdNumberPhoto(null);
            shopDetailDTO.setOwnerName(null);
        }
        return new Response<>(shopDetailDTO);
    }

    /**
     * 新增店铺
     */
    @PostMapping("/add")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> addSave(@RequestBody ShopQuery request) {
        ShopValidator validator = new ShopValidator();
        if (!validator.onShopName(request.getShopName()).onOwnerMobile(request.getOwnerMobile()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Date datetime = this.getServerTime();
        Long userId = this.getUserToken().getId();
        Shop shop = new Shop();
        BeanUtils.copyProperties(request, shop);
        shop.setCreateAdminId(userId);
        shop.setStatus(DataStatus.Y.getCode());
        shop.setCreateTime(datetime);
        shop.setModifyTime(datetime);
        shop.setStatus(DataStatus.Y.getCode());
        ShopConfig shopConfig = new ShopConfig();
        shopConfig.setName(request.getShopName());
        shopConfig.setStatus(DataStatus.Y.getCode());
        shopConfig.setModifyTime(datetime);
        shopConfig.setCreateTime(datetime);
        shopService.createShopAndShopConfig(shop, shopConfig);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 修改店铺
     */
    @PostMapping("/edit")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> editSave(@RequestBody ShopQuery shopQuery) {
        if (shopService.selectShopById(shopQuery.getId()) == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在");
        }
        Shop shop = new Shop(shopQuery);
        shopService.updateShop(shop);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除店铺
     */
    @PostMapping("/remove")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> remove(@RequestBody Shop request) {
        if (shopService.selectShopById(request.getId()) == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在");
        }
        Shop shop = new Shop();
        shop.setId(request.getId());
        shop.setStatus(DataStatus.N.getCode());
        shop.setModifyTime(this.getServerTime());
        shopService.updateShop(shop);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 批量删除店铺
     *
     * @param ids 店铺ID列表，逗号分隔
     */
    @PostMapping("/batch/remove")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> removeBatch(@NonNull String ids) {
        return new Response<>(OK, SUCCESS, shopService.deleteShopByIds(ids));
    }
}
