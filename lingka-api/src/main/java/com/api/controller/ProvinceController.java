package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Province;
import com.dto.ProvinceDTO;
import com.query.ProvinceQuery;
import com.github.pagehelper.PageInfo;
import com.service.CityService;
import com.service.DistrictService;
import com.service.ProvinceService;

/**
 * 省份控制器
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
public class ProvinceController extends BaseController {

    @Autowired
    private ProvinceService provinceService;

    /**
     * 增加省份
     */
    @RequestMapping(value = "/v1/province/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody Province province) {
        Date datetime = this.getServerTime();
        province.setStatus(DataStatus.Y.getCode());
        province.setModifyTime(datetime);
        province.setCreateTime(datetime);
        provinceService.create(province);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询省份列表
     */
    @RequestMapping(value = "/v1/province/query", method = RequestMethod.POST)
    public Response<PageInfo<ProvinceDTO>> query(@RequestBody ProvinceQuery provinceQuery) {
        provinceQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProvinceDTO> pageInfo = provinceService.find(provinceQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询省份列表
     */
    @RequestMapping(value = "/v1/province/all/query", method = RequestMethod.POST)
    public Response<List<ProvinceDTO>> queryAll(@RequestBody ProvinceQuery provinceQuery) {
        provinceQuery.setStatus(DataStatus.Y.getCode());
        List<ProvinceDTO> provinceDTOs = provinceService.findAll(provinceQuery);
        return new Response<>(provinceDTOs);
    }


    /**
     * 修改省份
     */
    @RequestMapping(value = "/v1/province/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Province province) {
        Date datetime = this.getServerTime();
        province.setModifyTime(datetime);
        provinceService.modifyById(province);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除省份
     */
    @RequestMapping(value = "/v1/province/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProvinceQuery request) {
        Date datetime = this.getServerTime();
        Province province = new Province();
        province.setId(request.getId());
        province.setStatus(DataStatus.N.getCode());
        province.setModifyTime(datetime);
        provinceService.modifyById(province);
        return new Response<>(OK, SUCCESS);
    }


}
