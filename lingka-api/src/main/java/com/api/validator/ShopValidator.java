package com.api.validator;

import com.aliyun.oss.internal.OSSUtils;

public class ShopValidator extends Validator {
    public ShopValidator onShopName(String nickname) {
        if (this.isEmpty(nickname)) {
            this.addAttribute(errors, "请输入店铺名称");
            this.result = false;
        }
        if (!this.isPrintable(nickname)) {
            this.addAttribute(errors, "店铺名称有不可见字符");
            this.result = false;
        }
        return this;
    }

    public ShopValidator onOwnerMobile(String ownerMobile) {
        if (this.isEmpty(ownerMobile)) {
            this.addAttribute(errors, "请输入店铺拥有者手机号");
            this.result = false;
        }
        return this;
    }

    public ShopValidator onPhoto(String urls) {
        if (urls != null) {
            String[] urlList = urls.split(",");
            if (urlList.length > 0 && !urlList[0].isEmpty()) {
                for (String key : urlList) {
                    if (!OSSUtils.validateObjectKey(key)) {
                        this.addAttribute(errors, "图片地址非法");
                        this.result = false;
                        break;
                    }
                }
            }
        }
        return this;
    }

    public ShopValidator onAddress(String address) {
        if (!this.isPrintable(address)) {
            this.addAttribute(errors, "地址有不可见字符");
            this.result = false;
        }
        return this;
    }
}
