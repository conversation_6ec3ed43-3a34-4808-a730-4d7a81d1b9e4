package com.api.validator;

import com.api.constant.App;
import com.common.bean.Response;

public class UserValidator extends Validator {
	public UserValidator onNickname(String nickname){
		if(this.isEmpty(nickname)){
			this.addAttribute(errors, "请输入昵称");
			this.result = false;
		}
		return this;
	}

	public UserValidator onAvatar(String avatar){
		if(this.isEmpty(avatar)){
			this.addAttribute(errors, "请输入头像");
			this.result = false;
		}
		return this;
	}

	public UserValidator onLoginCode(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入登录授权码");
			this.result = false;
		}
		return this;
	}

	public UserValidator onMobileCode(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入获取手机号授权码");
			this.result = false;
		}
        if (!str.matches("^[A-Za-z0-9_-]{6,128}$") || this.url(str)) {
            this.addAttribute(errors, "code格式非法");
            this.result = false;
        }
		return this;
	}

}
