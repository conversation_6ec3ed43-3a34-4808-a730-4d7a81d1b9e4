package com.api.config;

import java.io.Writer;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import io.jsonwebtoken.ExpiredJwtException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.api.constant.App;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.Device;
import com.common.constant.Platform;
import com.common.constant.TokenType;
import com.common.util.EncryptUtil;
import com.common.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;


public class AuthenticationInterceptor implements HandlerInterceptor {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Autowired private RedisTemplate<String, String> redisTemplate;
	
	@Override
	public boolean preHandle(HttpServletRequest request,HttpServletResponse response, Object handler) throws Exception {

		String platform = request.getHeader(App.HTTP_HEADER_APP_PLATFORM);
		String version = request.getHeader(App.HTTP_HEADER_APP_VERSION);
		String agent = request.getHeader(App.HTTP_HEADER_APP_AGENT);
		if(!StringUtil.isEmpty(platform)){
			logger.info("http header {} : {}",App.HTTP_HEADER_APP_PLATFORM, platform);
		}
		if(!StringUtil.isEmpty(version)){
			logger.info("http header {} : {}",App.HTTP_HEADER_APP_VERSION, version);
		}
		if(!StringUtil.isEmpty(agent)){
			logger.info("http header {} : {}",App.HTTP_HEADER_APP_AGENT, agent);
		}
		String token = request.getHeader(App.HTTP_HEADER_APP_TOKEN);
		try{
			if(!StringUtil.isEmpty(token)){
				JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
				logger.info("token id : {}",jsonWebToken.getId());
				logger.info("token type : {}",jsonWebToken.getType());
				logger.info("user id : {}",jsonWebToken.getUserId());
				// 判断终端是终端设备类型
				if (Platform.get(platform) == null) {
					// 未携带平台标示, 重新登录
					this.print(request, response,this.getJSON(new Response<Object>(Response.LOGOUT,Response.LOGOUT_MESSAGE)));
					return false;
				}
				// 终端设备类型
				Device device = Platform.DEVIVCE.get(platform);
				logger.info("-----------> {}", device.name());
//				// 获取凭证类型
				if (TokenType.C.name().equals(jsonWebToken.getType())) {
					// 普通用户
				} else if(TokenType.S.name().equals(jsonWebToken.getType())) {
					// 系统用户
				} else {
					// 未知用户
					this.print(request, response,this.getJSON(new Response<Object>(Response.FORBIDDEN,Response.FORBIDDEN_MESSAGE)));
					return false;
				}
				// 凭证比较,是否为最后一次登录凭证
				String key = CacheKey.USER_TOKEN + jsonWebToken.getType() + device.name() + String.valueOf(jsonWebToken.getUserId());
				String lastToken = this.redisTemplate.opsForValue().get(key);
				logger.info("last token : {}",lastToken);
				if (token.equals(lastToken)) {
					// 凭证相符

				} else {
					// 重新登录
					this.print(request, response,this.getJSON(new Response<Object>(Response.LOGOUT,Response.LOGOUT_MESSAGE)));
					return false;
				}
				// 校验类型是否符合
				if (handler instanceof HandlerMethod) {
					HandlerMethod handlerMethod = (HandlerMethod) handler;
					Method method = handlerMethod.getMethod();
					Token tokenAnnotation = method.getAnnotation(Token.class);
					if (tokenAnnotation != null && tokenAnnotation.tokenType() != null) {
						List<String> tokenTypes = new ArrayList<>();
						for (TokenType tokenType : tokenAnnotation.tokenType()) {
							tokenTypes.add(tokenType.name());
						}
						if (tokenTypes.contains(jsonWebToken.getType())) {
							// 凭证相符
						} else {
							// 无权限
							this.print(request, response,this.getJSON(new Response<Object>(Response.FORBIDDEN,Response.FORBIDDEN_MESSAGE)));
							return false;
						}
					}
				}
			} else {
				if (handler instanceof HandlerMethod) {
					// 判断接口是否需要凭证
					HandlerMethod handlerMethod = (HandlerMethod) handler;
					Method method = handlerMethod.getMethod();
					if (method.getAnnotation(Token.class) != null) {
						// 重新登录
						this.print(request, response,this.getJSON(new Response<Object>(Response.LOGOUT,Response.LOGOUT_MESSAGE)));
						return false;
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			this.print(request, response,this.getJSON(new Response<Object>(Response.LOGOUT,Response.LOGOUT_MESSAGE)));
			return false;
		}
		return true;
	}
	
	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

	}
	
	private void print(HttpServletRequest request,HttpServletResponse response, String data){
		Writer writer = null;
		try{
			response.setContentType(App.APPLICATION_JSON_UTF8_VALUE);
			writer = response.getWriter();
			writer.write(data);
			writer.flush();
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
		}finally{
			if(writer != null){
				try{
					writer.close();
				}catch (Exception e) {
					logger.error(e.getMessage(),e);
				}
			}
		}
	}
	
	private String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
}
