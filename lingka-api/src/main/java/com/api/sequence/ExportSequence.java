package com.api.sequence;

import java.util.concurrent.atomic.AtomicInteger;

import com.api.constant.App;
import com.common.util.DateUtil;

public class ExportSequence {
    private static final int SEQ_START = 1000;
    private static final int SEQ_MAX = 9999;
    private static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(SEQ_START);
    public static final int nextValue(){
        ATOMIC_INTEGER.compareAndSet(SEQ_MAX + 1, SEQ_START);
        return ATOMIC_INTEGER.getAndIncrement();
    }
    public static final String get() {
        return String.valueOf(DateUtil.getServerTime("yyyyMMdd")) + App.ID + nextValue();
    }

}
