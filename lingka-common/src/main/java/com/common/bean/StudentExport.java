package com.common.bean;

import java.util.List;

public class StudentExport extends ExportMessage{


    /**
     * 通讯录姓名
     */
    private String name;

    /**
     * 用户角色编号
     */
    private Long roleId;

    /**
     * 用户身份
     */
    private String userCatalog;

    private List<Long> departmentIds;

    private String mobile;

    private String register;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getUserCatalog() {
        return userCatalog;
    }

    public void setUserCatalog(String userCatalog) {
        this.userCatalog = userCatalog;
    }

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public String getRegister() {
        return register;
    }

    public void setRegister(String register) {
        this.register = register;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }
}
