package com.common.bean;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.common.util.StringUtil;

public class Pager {
	public static final String PAGE = "page";
	public static final String FIRST = "1";
	//private List<T> list; //对象记录结果集
	private int total = 0; // 总记录数
	private int limit = 10; // 每页显示记录数
	private int pages = 1; // 总页数
	private int page = 1; // 当前页
	private String url;
	
	private boolean isFirstPage = false;        //是否为第一页
    private boolean isLastPage = false;         //是否为最后一页
    private boolean hasPreviousPage = false;   //是否有前一页
    private boolean hasNextPage = false;       //是否有下一页
    
	private int navigatePages = 8; //导航页码数
	private int[] navigatePageNumbers;  //所有导航页号
	
	public Pager(int total, int pageNumber) {
		init(total, pageNumber, limit);
	}
	
	public Pager(int total, int pageNumber, int limit) {
		init(total, pageNumber, limit);
	}
	
	private void init(int total, int pageNumber, int limit){
		//设置基本参数
		this.total = total;
		this.limit = limit;
		this.pages = (this.total-1) / this.limit+1;
		
		//根据输入可能错误的当前号码进行自动纠正
		if(pageNumber<1){
			this.page=1;
		}else if(pageNumber>this.pages){
			this.page=this.pages;
		}else{
			this.page=pageNumber;
		}
		
		//基本参数设定之后进行导航页面的计算
		calcNavigatePageNumbers();
		
		//以及页面边界的判定
		judgePageBoudary();
	}
	
	/**
	 * 计算导航页
	 */
	private void calcNavigatePageNumbers(){
		//当总页数小于或等于导航页码数时
		if(pages<=navigatePages){
			navigatePageNumbers=new int[pages];
			for(int i=0;i<pages;i++){
				navigatePageNumbers[i]=i+1;
			}
		}else{ //当总页数大于导航页码数时
			navigatePageNumbers=new int[navigatePages];
			int startNum=page-navigatePages/2;
			int endNum=page+navigatePages/2;
			
			if(startNum<1){
				startNum=1;
				//(最前navPageCount页
				for(int i=0;i<navigatePages;i++){
					navigatePageNumbers[i]=startNum++;
				}
			}else if(endNum>pages){
				endNum=pages;
				//最后navPageCount页
				for(int i=navigatePages-1;i>=0;i--){
					navigatePageNumbers[i]=endNum--;
				}
			}else{
				//所有中间页
				for(int i=0;i<navigatePages;i++){
					navigatePageNumbers[i]=startNum++;
				}
			}
		}
	}

	/**
	 * 判定页面边界
	 */
	private void judgePageBoudary(){
		isFirstPage = page == 1;
		isLastPage = page == pages && page !=1;
		hasPreviousPage = page!=1;
		hasNextPage = page!=pages;
	}
	
	
	/*public void setList(List<T> list) {
		this.list = list;
	}*/

	/**
	 * 得到当前页的内容
	 * @return {List}
	 */
	/*public List<T> getList() {
		return list;
	}*/

	/**
	 * 得到记录总数
	 * @return {int}
	 */
	public int getTotal() {
		return total;
	}
	public void setTotal(int total){
		this.total = total;
	}
	/**
	 * 得到每页显示多少条记录
	 * @return {int}
	 */
	public int getLimit() {
		return limit;
	}
	public void setLimit(int limit) {
		this.limit = limit;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url, Map<String,String> map) {
		StringBuilder buffer = new StringBuilder(url).append("?");
		if(map != null && !map.isEmpty()){
			int counter = 0;
			Set<Entry<String,String>> entrySet = map.entrySet();
			for(Entry<String,String> entry : entrySet){
				String key = entry.getKey();
				String value = entry.getValue();
				try{
					if(counter > 0){
						if(value != null){
							buffer.append("&").append(key).append("=").append(URLEncoder.encode(value, StandardCharsets.UTF_8.name()));
						}else{
							buffer.append("&").append(key).append("=");
						}
					}else{
						if(value != null){
							buffer.append(key).append("=").append(URLEncoder.encode(value, StandardCharsets.UTF_8.name()));
						}else{
							buffer.append(key).append("=");
						}
					}
				}catch (Exception e) {
					throw new RuntimeException(e);
				}
				counter++;
			}
		}
		if(buffer.toString().endsWith("?")){
			
		}else{
			buffer.append("&");
		}
		this.url = buffer.toString();
	}
	public void setUrl(String url, NameValuePair... pairs) {
		this.url = StringUtil.getRequestParameters(url, pairs);
	}
	public void setUrl(String url, List<NameValuePair> pairs) {
		this.url = StringUtil.getRequestParameters(url, pairs.toArray(new NameValuePair[pairs.size()]));
	}
	/**
	 * 得到页面总数
	 * @return {int}
	 */
	public int getPages() {
		return pages;
	}

	/**
	 * 得到当前页号
	 * @return {int}
	 */
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public int getOffset() {
		return (page < 1 ? 1 : page - 1) * limit;
	}
	public int getPreviousPage(){
		if(hasPreviousPage){
			return this.getPage() - 1;
		}
		return this.getPage();
	}
	public int getNextPage(){
		if(hasNextPage){
			return this.getPage() + 1;
		}
		return this.getPage();
	}

	/**
	 * 得到所有导航页号 
	 * @return {int[]}
	 */
	public int[] getNavigatePageNumbers() {
		return navigatePageNumbers;
	}

	public boolean isFirstPage() {
		return isFirstPage;
	}

	public boolean isLastPage() {
		return isLastPage;
	}
	public int getFirstPage() {
		return 1;
	}
	public int getLastPage() {
		return pages;
	}
	public boolean hasPreviousPage() {
		return hasPreviousPage;
	}

	public boolean hasNextPage() {
		return hasNextPage;
	}

	public String toString(){
		String str=new String();
		str= "[" +
			"total="+total+
			",pages="+pages+
			",pageNumber="+page+
			",limit="+limit+
			//",navigatePages="+navigatePages+
			",isFirstPage="+isFirstPage+
			",isLastPage="+isLastPage+
			",hasPreviousPage="+hasPreviousPage+
			",hasNextPage="+hasNextPage+
			",navigatePageNumbers=";
		int len=navigatePageNumbers.length;
		if(len>0)str+=(navigatePageNumbers[0]);
		for(int i=1;i<len;i++){
			str+=(" "+navigatePageNumbers[i]);
		}
		//sb+=",list="+list;
		str+="]";
		return str;
	}
	public String getTotalDisplay(){
		StringBuilder buffer = new StringBuilder();
		if(pages > 1){
			buffer.append("<ul class=\"pagination\">");
			buffer.append("<li class=\"\"><span>").append("共").append("</span></li>");
			buffer.append("<li class=\"active\"><span>").append(this.getTotal()).append("</span></li>");
			buffer.append("<li class=\"\"><span>").append("条").append("</span></li>");
			buffer.append("<li class=\"active\"><span>").append(this.getPages()).append("</span></li>");
			buffer.append("<li class=\"\"><span>").append("页").append("</span></li>");
			buffer.append("</ul>");
		}
		return buffer.toString();
	}
	public String getDisplay(){
		StringBuilder buffer = new StringBuilder();
		int navPageLength = this.getNavigatePageNumbers().length / 2;
		if(pages > 1){
			buffer.append("<ul class=\"pagination\">");
   			if((this.getPage() - navPageLength) > this.getFirstPage()){
   				buffer.append("<li><a href=\"").append(this.getUrl()).append("&").append(PAGE).append("=").append(this.getFirstPage()).append("\">|&lt;</a></li>");
   			}
   			if(!this.isFirstPage()){
   				buffer.append("<li><a href=\"").append(this.getUrl()).append("&").append(PAGE).append("=").append(this.getPreviousPage()).append("\">&lt;</a></li>");
   			}
   			for(int n : this.getNavigatePageNumbers()){
   				if(n == this.getPage()){
   					buffer.append("<li class=\"active\"><span>").append(n).append("</span></li>");
   				}else{
   					buffer.append("<li><a href=\"").append(this.getUrl()).append("&").append(PAGE).append("=").append(n).append("\">").append(n).append(" </a></li>");
   				}
   			}
   			if(!this.isLastPage()){
   				buffer.append("<li><a href=\"").append(this.getUrl()).append("&").append(PAGE).append("=").append(this.getNextPage()).append("\">&gt;</a></li>");
   			}
   			if((this.getPage() + navPageLength) <= this.getLastPage()){
   				buffer.append("<li><a href=\"").append(this.getUrl()).append("&").append(PAGE).append("=").append(this.getLastPage()).append("\">&gt;|</a></li>");
   			}
   			buffer.append("</ul>");
		}
		return buffer.toString();
	}
}