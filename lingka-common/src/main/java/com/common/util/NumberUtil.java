package com.common.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NumberUtil {
	/**
	 * 正整数
	 * @param str
	 * @return
	 */
	public static boolean isNumeric(String str){
		for(int i=str.length();--i>=0;){
		      int chr=str.charAt(i);
		      if(chr<48 || chr>57)
		         return false;
		   }
		   return true;
	}
	public static boolean isInteger(String str){
		try{
			Integer.parseInt(str);
		}catch (Exception e) {
			return false;
		}
		return true;
	}
	public static String compare(String str1,String str2){
		String res = null;
		try{
			int flag = new BigDecimal(str1).compareTo(new BigDecimal(str2));
			res = String.valueOf(flag);
		}catch (Exception e) {			
		}
		return res;
	}
	/**
	 * 四舍五入
	 * @param str
	 * @param formater
	 * @return
	 */
	public static String format(String str,String formater){
		try{
			BigDecimal bigDecimal = new BigDecimal(str);
			DecimalFormat df = new DecimalFormat(formater);
			bigDecimal.setScale(1,BigDecimal.ROUND_HALF_UP);
			return df.format(bigDecimal);
		}catch (Exception e) {	
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 四舍五入
	 * @param str
	 * @param formater
	 * @return
	 */
	public static String format(BigDecimal bigDecimal,String formater){
		try{
			DecimalFormat df = new DecimalFormat(formater);
			bigDecimal.setScale(1,BigDecimal.ROUND_HALF_UP);
			return df.format(bigDecimal);
		}catch (Exception e) {	
			throw e;
		}
	}
	/**
	 * 获取N位不重复的数字
	 * @param size
	 * @return
	 */
	public static Set<Integer> getUniqueRandomNumber(int size) {
		if(size > 10){
			throw new RuntimeException();
		}
        Set<Integer> set = new HashSet<Integer>();  
        Random random = new Random();  
        while (set.size() < size) { 
            set.add(random.nextInt(10));  
        }
        return set;
    }
	/**
	 * 获取N位数字
	 * @param size
	 * @return
	 */
	public static List<Integer> getRandomNumber(int size) {
		if(size > 10){
			throw new RuntimeException();
		}
        List<Integer> list = new ArrayList<Integer>();  
        Random random = new Random();  
        for(int i = 0; i < size; i++){
        		list.add(random.nextInt(10));
        }
        return list;
    }
	/**
	 * 加法
	 * @param s1
	 * @param s2
	 * @return
	 */
	public static String add(String s1,String s2){
		BigDecimal bigDecimal1 = new BigDecimal(s1);
		BigDecimal bigDecimal2 = new BigDecimal(s2);
		BigDecimal bigDecimalResult = bigDecimal1.add(bigDecimal2);
		return bigDecimalResult.toString();
	}
	/**
	 * 减法
	 * @param s1
	 * @param s2
	 * @return
	 */
	public static String subtract(String s1,String s2){
		BigDecimal bigDecimal1 = new BigDecimal(s1);
		BigDecimal bigDecimal2 = new BigDecimal(s2);
		BigDecimal bigDecimalResult = bigDecimal1.subtract(bigDecimal2);
		return bigDecimalResult.toString();
	}
	/**
	 * 乘法
	 * @param s1
	 * @param s2
	 * @return
	 */
	public static String multiply(String s1,String s2){
		BigDecimal bigDecimal1 = new BigDecimal(s1);
		BigDecimal bigDecimal2 = new BigDecimal(s2);
		BigDecimal bigDecimalResult = bigDecimal1.multiply(bigDecimal2);
		return bigDecimalResult.toString();
	}
	/**
	 * 除法
	 * @param s1
	 * @param s2
	 * @return
	 */
	public static String divide(String s1,String s2){
		if(!StringUtil.isEmpty(s1) && !StringUtil.isEmpty(s2)){
			return divide(s1,s2,2);
		}
		return null;
		
	}
	/**
	 * 除法
	 * @param s1
	 * @param s2
	 * @return
	 */
	public static String divide(String s1,String s2,int precision){
		MathContext mc = new MathContext(precision, RoundingMode.HALF_UP);
		BigDecimal bigDecimal1 = new BigDecimal(s1);
		BigDecimal bigDecimal2 = new BigDecimal(s2);
		BigDecimal bigDecimalResult = bigDecimal1.divide(bigDecimal2,mc);
		return bigDecimalResult.toString();
	}
	/**
	 * 验证手机号
	 * @param mobile
	 * @return
	 */
	public static boolean isMobile(String mobile) {
		Pattern p = Pattern.compile("^((13[0-9])|(15[^4,\\D])|(17[0-9])|(18[0-9]))\\d{8}$");
		Matcher m = p.matcher(mobile);
		return m.matches();
	}

	/**
	 * 验证是不是数字
	 * @param keyWord
	 * @return
	 */
	public static boolean isNumber(String keyWord){
		Pattern pattern = Pattern.compile("^([+-]?)\\d*\\.?\\d+$");
		Matcher matcher = pattern.matcher(keyWord);
		return matcher.matches();
	}
}
