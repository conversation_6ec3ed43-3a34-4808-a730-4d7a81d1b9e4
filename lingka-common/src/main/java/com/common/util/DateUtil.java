package com.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;


public class DateUtil {
	public static String getNextDay(String appDate) {
		return getFutureDay(appDate, "yyyy-MM-dd", 1);
	}
	public static String getFutureDay(String appDate, int days) {
		return getFutureDay(appDate, "yyyy-MM-dd", days);
	}
	public static String getFutureMonth(String appDate, String format, int months) {
		String future = "";
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			Date date = simpleDateFormat.parse(appDate);
			calendar.setTime(date);
			calendar.add(Calendar.MONTH, months);
			date = calendar.getTime();
			future = simpleDateFormat.format(date);
		}catch(Exception e){
			e.printStackTrace();
		}
		return future;
	}
	public static String getFutureDay(String appDate, String format, int days) {
		String future = "";
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			Date date = simpleDateFormat.parse(appDate);
			calendar.setTime(date);
			calendar.add(Calendar.DATE, days);
			date = calendar.getTime();
			future = simpleDateFormat.format(date);
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return future;
	}
	public static Date getFutureDay(Date date,int days) {
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DATE, days);
			return calendar.getTime();
		}catch(Exception e){
			throw new RuntimeException(e);
		}
	}
	public static Date getFutureHour(Date date,int hours) {
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.HOUR, hours);
			return calendar.getTime();
		}catch(Exception e){
			throw new RuntimeException(e);
		}
	}
	public static Date getFutureMonth(Date date,int months) {
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.MONTH, months);
			return calendar.getTime();
		}catch(Exception e){
			throw new RuntimeException(e);
		}
	}
	public static int getDifferenceDay(String minDate,String maxDate,String format) {
		long diffTime = 0;
		try{
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			long minTime = simpleDateFormat.parse(minDate).getTime();
			long maxTime = simpleDateFormat.parse(maxDate).getTime();
			long diff = maxTime - minTime;
			diffTime =  diff / (24 * 60 * 60 * 1000);
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return (int)diffTime;
	}

	public static int getDifferenceDay(Date minTime,Date maxTime) {
		long diffTime = 0;
		try{
			long diff = maxTime.getTime() - minTime.getTime();
			diffTime =  diff / (24 * 60 * 60 * 1000);
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return (int)diffTime;
	}
	public static int getDifferenceHour(Date minTime,Date maxTime) {
		long diffTime = 0;
		try{
			long diff = maxTime.getTime() - minTime.getTime();
			diffTime =  diff / (60 * 60 * 1000);
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return (int)diffTime;
	}

	public static int getDifferenceSecond(Date minTime,Date maxTime) {
		long diffTime = 0;
		try{
			long diff = maxTime.getTime() - minTime.getTime();
			diffTime =  diff /  1000;
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return (int)diffTime;
	}


	public static long getDifferenceTime(String minDate,String maxDate,String format) {
		long diffTime = 0;
		try{
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			long minTime = simpleDateFormat.parse(minDate).getTime();
			long maxTime = simpleDateFormat.parse(maxDate).getTime();
			diffTime = maxTime - minTime;
		}catch(Exception e){
			throw new RuntimeException(e);
		}
		return diffTime;
	}
	public static Calendar getEarliestDate(Calendar currentDate, int dayOfWeek,
			int hourOfDay, int minuteOfHour, int secondOfMinite) {
		//计算当前时间的WEEK_OF_YEAR,DAY_OF_WEEK, HOUR_OF_DAY, MINUTE,SECOND等各个字段值
		int currentWeekOfYear = currentDate.get(Calendar.WEEK_OF_YEAR);
		int currentDayOfWeek = currentDate.get(Calendar.DAY_OF_WEEK);
		int currentHour = currentDate.get(Calendar.HOUR_OF_DAY);
		int currentMinute = currentDate.get(Calendar.MINUTE);
		int currentSecond = currentDate.get(Calendar.SECOND);

		//如果输入条件中的dayOfWeek小于当前日期的dayOfWeek,则WEEK_OF_YEAR需要推迟一周
		boolean weekLater = false;
		if (dayOfWeek < currentDayOfWeek) {
			weekLater = true;
		} else if (dayOfWeek == currentDayOfWeek) {
			//当输入条件与当前日期的dayOfWeek相等时，如果输入条件中的
			//hourOfDay小于当前日期的
			//currentHour，则WEEK_OF_YEAR需要推迟一周
			if (hourOfDay < currentHour) {
				weekLater = true;
			} else if (hourOfDay == currentHour) {
                 //当输入条件与当前日期的dayOfWeek, hourOfDay相等时，
                 //如果输入条件中的minuteOfHour小于当前日期的
				//currentMinute，则WEEK_OF_YEAR需要推迟一周
				if (minuteOfHour < currentMinute) {
					weekLater = true;
				} else if (minuteOfHour == currentSecond) {
                     //当输入条件与当前日期的dayOfWeek, hourOfDay，
                     //minuteOfHour相等时，如果输入条件中的
                    //secondOfMinite小于当前日期的currentSecond，
                    //则WEEK_OF_YEAR需要推迟一周
					if (secondOfMinite < currentSecond) {
						weekLater = true;
					}
				}
			}
		}
		if (weekLater) {
			//设置当前日期中的WEEK_OF_YEAR为当前周推迟一周
			currentDate.set(Calendar.WEEK_OF_YEAR, currentWeekOfYear + 1);
		}
		// 设置当前日期中的DAY_OF_WEEK,HOUR_OF_DAY,MINUTE,SECOND为输入条件中的值。
		currentDate.set(Calendar.DAY_OF_WEEK, dayOfWeek);
		currentDate.set(Calendar.HOUR_OF_DAY, hourOfDay);
		currentDate.set(Calendar.MINUTE, minuteOfHour);
		currentDate.set(Calendar.SECOND, secondOfMinite);
		return currentDate;
	}
	/**
	 * 获取月日历
	 * @param month
	 * @param format
	 * @return
	 */
	public static String[] getCalendar(String month,String format){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(parse(month,format));
		int currMonthDays = calendar.getActualMaximum(Calendar.DATE);
		String[] monthDayArray = new String[currMonthDays];
		for(int i = 0; i < currMonthDays; i++){
			int n = i + 1;
			if(n < 10){
				monthDayArray[i] = new StringBuffer("0").append(String.valueOf(n)).toString();
			}else{
				monthDayArray[i] = String.valueOf(n);
			}
		}
		return monthDayArray;
	}
	/**
	 * 获取时间段内日期
	 * @param startDateStr
	 * @param endDateStr
	 * @param format
	 * @return
	 */
	public static List<String> getCalendar(String startDateStr,String endDateStr,String format){
		Date startDate = parse(startDateStr, format);
		Date endDate = parse(endDateStr, format);
		List<String> dateList = new ArrayList<String>();
		dateList.add(startDateStr);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		while(true){
			calendar.add(Calendar.DAY_OF_MONTH, 1);
			Date date = calendar.getTime();
			if(!endDate.after(date)){
				break;
			}
			dateList.add(format(date,format));
		}
		if (startDate.compareTo(endDate) != 0){
			dateList.add(endDateStr);
		}
		return dateList;
	}


	public static List<String> getCalendar(Date startDate,Date endDate,String format){
		String startDateStr = DateUtil.format(startDate,format);
		String endDateStr = DateUtil.format(endDate,format);
		List<String> dateList = new ArrayList<>();
		dateList.add(startDateStr);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		while(true){
			calendar.add(Calendar.DAY_OF_MONTH, 1);
			Date date = calendar.getTime();
			if(!endDate.after(date)){
				break;
			}
			dateList.add(format(date,format));
		}
		if (startDate.compareTo(endDate) != 0){
			dateList.add(endDateStr);
		}
		return dateList;
	}

	public static List<String> getYears(String startDateStr,String endDateStr,String format){
		Date startDate = parse(startDateStr, format);
		Date endDate = parse(endDateStr, format);
		List<String> dateList = new ArrayList<String>();
		dateList.add(startDateStr);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		while(true){
			calendar.add(Calendar.YEAR, 1);
			Date date = calendar.getTime();
			if(!endDate.after(date)){
				break;
			}
			dateList.add(format(date,format));
		}
		dateList.add(endDateStr);
		return dateList;
	}

	/**
	 * 获取时间段内日期(按小时)
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static List<Date> getHours(Date startDate,Date endDate,Integer length){
		List<Date> dateList = new ArrayList<Date>();
		dateList.add(startDate);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		while(true){
			calendar.add(Calendar.HOUR_OF_DAY, length);
			Date date = calendar.getTime();
			if(!endDate.after(date)){
				break;
			}
			dateList.add(date);
		}
		dateList.add(endDate);
		return dateList;
	}

	/**
	 * 获取这两个时间段内,每周的周一
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static List<Date> getWeekStartTime(Date startTime, Date endTime){
		List<Date> dateList = new ArrayList<>();
		Date weekStartTime = getWeekStartTime(startTime);
		Date weekEndTime = getWeekEndTime(endTime);
		dateList.add(weekStartTime);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(weekStartTime);
		while(true){
			calendar.add(Calendar.DATE,7);
			Date date = calendar.getTime();
			if(!weekEndTime.after(date)){
				break;
			}
			dateList.add(date);
		}
		dateList.add(weekEndTime);
		return dateList;
	}

	/**
	 * 获取这两个时间段内,每天的开始时间
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static List<Date> getDayStartTime(Date startTime, Date endTime){
		List<Date> dateList = new ArrayList<>();
		dateList.add(startTime);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startTime);
		while(true){
			calendar.add(Calendar.DATE,1);
			Date date = calendar.getTime();
			if(!endTime.after(date)){
				break;
			}
			dateList.add(date);
		}
		dateList.add(endTime);
		return dateList;
	}

	/**
	 * 字符串转日期
	 * @param str
	 * @param format
	 * @return
	 */
	public static Date parse(String str,String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Date date = null;
		try {
			date = sdf.parse(str);
		} catch (ParseException e) {
			throw new RuntimeException("字符串转日期错误",e);
		}
		return date;
	}
	/**
	 * 日期转字符串
	 * @param date
	 * @param format
	 * @return
	 */
	public static String format(Date date,String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		return sdf.format(date);
	}
	/**
	 * 格式化时间
	 * @param str
	 * @param srcFormat
	 * @param targetFormat
	 * @return
	 */
	public static final String getFormatTime(String str,String srcFormat,String targetFormat){
		Date date = null;
		String retString = str;
		SimpleDateFormat srcFormatter = new SimpleDateFormat(srcFormat);
		try {
			date = srcFormatter.parse(str);
		} catch (ParseException e) {
			throw new RuntimeException("字符串转日期错误",e);
		}
		SimpleDateFormat targetFormatter = new SimpleDateFormat(targetFormat);
		retString = targetFormatter.format(date);
		return retString;
	}
	/**
	 * 比较两个日期字符串先后
	 * @param appDate1
	 * @param appDate2
	 * @param format
	 * @return
	 */
	public static final int compareTo(String appDate1,String appDate2,String format){
		Date date1 = parse(appDate1,format);
		Date date2 = parse(appDate2,format);
		return date1.compareTo(date2);
	}
	public static int getWeekFromTime(String time,String timeFormat){
        Calendar calendar = Calendar.getInstance();
        int weekIndex = 0;
        try {
        	 	SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
			calendar.setTime(sdf.parse(time));
			calendar.set(Calendar.DAY_OF_WEEK, 2);
	        weekIndex = calendar.get(Calendar.DAY_OF_WEEK) - 1;
	        if (weekIndex < 0){
	        		weekIndex = 0;
	        }
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
        return weekIndex;
	}



	/**
	 * 获取星期几
	 * 星期一返回 1   星期二返回 2  ....  星期日返回 0 (方便以后用集合取)
	 * @param datetime
	 * @return
	 */
	public static int getWeekDayFromTime(Date datetime){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(datetime);
		return calendar.get(Calendar.DAY_OF_WEEK) - 1;
	}

	public static String getChineseWeekFromTime(String time,String timeFormat){
		String[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
        return weekDays[getWeekFromTime(time, timeFormat)];
	}

	public static String getChineseWeekFromTime(Date datetime){
		String[] weekDays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
		Calendar calendar = Calendar.getInstance();
		int weekIndex = 0;
		calendar.setTime(datetime);
		weekIndex = calendar.get(Calendar.DAY_OF_WEEK) - 1;
		if (weekIndex < 0){
			weekIndex = 0;
		}
		return weekDays[weekIndex];
	}

	public static final String getServerTime(String format){
		Calendar calendar = Calendar.getInstance();
		Date date = calendar.getTime();
		return format(date, format);
	}
	public static final Date getServerTime(){
		Calendar calendar = Calendar.getInstance();
		Date date = calendar.getTime();
		return date;
	}
	public static final Date add(Date date,int unit,int time){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(unit, time);
		return calendar.getTime();
	}

	public static Date getFutureMinute(Date startTime, int minute) {
		try{
			Calendar calendar = GregorianCalendar.getInstance();
			calendar.setTime(startTime);
			calendar.add(Calendar.MINUTE, minute);
			Date futureDate = calendar.getTime();
			return futureDate;
		}catch(Exception e){
			throw new RuntimeException(e);
		}
	}

	public static Date getDayStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
	public static Date getDayEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
	public static Date getMonthStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
	public static Date getMonthEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
	}
	public static Date getYearStartTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}
	public static Date getYearEndTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, 1);
		calendar.add(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH,1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static Date getWeekStartTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setFirstDayOfWeek(Calendar.MONDAY);
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
		return getDayStartTime(calendar.getTime());
	}

	public static Date getWeekEndTime(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setFirstDayOfWeek(Calendar.MONDAY);
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
		calendar.add(Calendar.DATE,7);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	//获取上周的开始时间
	public static Date getLastWeekStartTime(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		//获取这一周里的一天
		int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
		if (dayOfWeek == 1) {
			dayOfWeek += 7;
		}
		cal.add(Calendar.DATE, 2 - dayOfWeek - 7);
		return getDayStartTime(cal.getTime());
	}

	// 获取上月开始时间
	public static Date getLastMonth(Date date){
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(getNowYear(date), getNowMonth(date) - 2, 1);
		return getDayStartTime(cal.getTime());
	}

	//获取今年是哪一年
	public static Integer getNowYear(Date date) {
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		return gc.get(Calendar.YEAR);
	}

	//获取本月是哪一月
	public static int getNowMonth(Date date) {
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		return gc.get(Calendar.MONTH) + 1;
	}
	//获取哪一天
	public static int getNowDay(Date date) {
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		return gc.get(Calendar.DAY_OF_MONTH);
	}

	// 秒转换成两位的时间，格式：HH:mm:ss
	public static String turnSecondsToTimestring(int seconds) {
		String result = "";
		int hour = 0, min = 0, second = 0;
		hour = seconds / 3600;
		min = (seconds - hour * 3600) / 60;
		second = seconds - hour * 3600 - min * 60;
		if (hour < 10) {
			result += "0" + hour + ":";
		} else {
			result += hour + ":";
		}
		if (min < 10) {
			result += "0" + min + ":";
		} else {
			result += min + ":";
		}
		if (second < 10) {
			result += "0" + second;
		} else {
			result += second;
		}

		return result;

	}
	// 秒转换时间，格式：×小时×分钟
	public static String secondsToTimeString(int seconds) {
		String result = "";
		int hour = 0, min = 0;
		hour = seconds / 3600;
		min = (seconds - hour * 3600) / 60;
		result += hour + "小时";
		result += min + "分钟";
		return result;

	}

	/**
	 * 判断两个时间段是否有交集
	 * @param aStartTime
	 * @param aEndTime
	 * @param bStartTime
	 * @param bEndTime
	 * @return
	 */
	public static boolean onUnion(Date aStartTime,Date aEndTime,Date bStartTime,Date bEndTime){
		long a = aStartTime.getTime();
		long b = aEndTime.getTime();
		long x = bStartTime.getTime();
		long y = bEndTime.getTime();
		if (x < b && a < y){
			return true;
		}else {
			return false;
		}
	}

	/**
	 * 比较两个时间是否在同一天
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static boolean isSameDate(Date date1, Date date2) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		boolean isSameYear = cal1.get(Calendar.YEAR) == cal2
				.get(Calendar.YEAR);
		boolean isSameMonth = isSameYear
				&& cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		boolean isSameDate = isSameMonth
				&& cal1.get(Calendar.DAY_OF_MONTH) == cal2
				.get(Calendar.DAY_OF_MONTH);
		return isSameDate;
	}

	/**
	 * 获取这两个时间段内,每月的一号
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public static List<String> getMonthStartTime(Date startTime, Date endTime){
		//开始时间
		Date startDate = getMonthStartTime(startTime);

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);
		// 获取开始年份和开始月份
		int startYear = calendar.get(Calendar.YEAR);
		int startMonth = calendar.get(Calendar.MONTH);
		// 获取结束年份和结束月份
		calendar.setTime(endTime);
		int endYear = calendar.get(Calendar.YEAR);
		int endMonth = calendar.get(Calendar.MONTH);
		//
		List<String> dateList = new ArrayList<>();
		for (int i = startYear; i <= endYear; i++) {
			String date = "";
			if (startYear == endYear) {
				for (int j = startMonth; j <= endMonth; j++) {
					if (j < 9) {
						date = i + "-0" + (j + 1);
					} else {
						date = i + "-" + (j + 1);
					}
					dateList.add(date);
				}

			} else {
				if (i == startYear) {
					for (int j = startMonth; j < 12; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						dateList.add(date);
					}
				} else if (i == endYear) {
					for (int j = 0; j <= endMonth; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						dateList.add(date);
					}
				} else {
					for (int j = 0; j < 12; j++) {
						if (j < 9) {
							date = i + "-0" + (j + 1);
						} else {
							date = i + "-" + (j + 1);
						}
						dateList.add(date);
					}
				}

			}

		}
		return dateList;
	}
	/**
	 * 日期月份推移
	 * @param num 推移数量  正数前推 负数后推
	 * @return
	 */
	public static Date getDateMonthPass( int num ){
		Date date = new Date();
		Calendar instance = Calendar.getInstance();
		instance.setTime(date);
		instance.add(Calendar.MONTH,num);
		return instance.getTime();
	}
}