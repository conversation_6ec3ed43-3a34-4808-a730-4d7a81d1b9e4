package com.common.util;

import java.io.IOException;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.SqlTimeSerializer;

public class CustomSqlTimeSerializer extends SqlTimeSerializer {
    TimeZone sh = TimeZone.getTimeZone("Asia/Shanghai");
    static String strTimeFormat = "HH:mm:ss";
    static final SimpleDateFormat sdf = new SimpleDateFormat(strTimeFormat);

    @Override
    public void serialize(Time value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        sdf.setTimeZone(sh);
        String formatted = sdf.format(value);
        gen.writeString(formatted);
    }
}
