package com.common.util;

import java.util.StringTokenizer;

import javax.servlet.http.HttpServletRequest;

public class IPUtil {
	public static String getClientIP(HttpServletRequest request){
		String ip = request.getHeader("X-Real-IP");
//		String ip = request.getHeader("X-Forwarded-For");
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
		    ip = request.getHeader("X-Forwarded-For");
		}
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Prolingka-Client-IP");
		}
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
		    ip = request.getHeader("WL-Prolingka-Client-IP");
		}
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
		    ip = request.getRemoteAddr();
		}
		StringTokenizer st = new StringTokenizer(ip, ",");
		while(st.hasMoreElements()){
			String str = (String)st.nextElement();
			if(!"unknown".equalsIgnoreCase(str)){
				ip = str;
			};
		}
		return ip;
	}
}
