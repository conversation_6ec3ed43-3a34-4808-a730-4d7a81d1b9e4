package com.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class POIUtil {

	static Logger logger = LoggerFactory.getLogger(POIUtil.class);
	/**
	 * 解析excel并以字符串数组的形式返回数据
	 * @param url 文件路径
	 * @return String[表][行][列]
	 * 增加：处理日期    lingkaalei 2021-11-15
	 */
	public static String[][] parse(String url){
		String[][][] dataArray = null;
		Workbook workBook = null;
		try {
			File file = new File(url);
			InputStream is = new FileInputStream(file);
			if(url.lastIndexOf(".xlsx") != -1){
				workBook = new XSSFWorkbook(is);
			}else if(url.lastIndexOf(".xls") != -1){
				workBook = new HSSFWorkbook(is);
			}else{

			}
			int sheetNum = workBook.getNumberOfSheets();
			if(sheetNum > 0){
				dataArray = new String[sheetNum][][];
				for(int i = 0; i < sheetNum; i++){
					Sheet sheet = workBook.getSheetAt(i);
					if(sheet != null){
						int rowNum = sheet.getLastRowNum() + 1;
						dataArray[i] = new String[rowNum][];
						if(rowNum > 0){
							int maxCellNum = 0;
							for(int j = 0; j < rowNum; j++){
								Row row = sheet.getRow(j);
								if(row != null) {
									int cellNum = row.getLastCellNum();
									if (cellNum > maxCellNum){
										maxCellNum = cellNum;
									}
								}
							}
							for(int j = 0; j < rowNum; j++){
								Row row = sheet.getRow(j);
								if(row != null){
									dataArray[i][j] = new String[maxCellNum];
									if(maxCellNum > 0){
										DecimalFormat decimalFormat = new DecimalFormat("#");
										for(int k = 0; k < maxCellNum; k++){
											Cell cell = row.getCell(k);
											if(cell != null){
												if(CellType.NUMERIC.equals(cell.getCellType())){
													if(DateUtil.isCellDateFormatted(cell)){
														Date d = cell.getDateCellValue();
														DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
														dataArray[i][j][k] = dateFormat.format(d);
													}else {
														dataArray[i][j][k] = decimalFormat.format(cell.getNumericCellValue());
													}
												}else {
													cell.setCellType(CellType.STRING);
													dataArray[i][j][k] = cell.getStringCellValue();
												}
											}else {
												dataArray[i][j][k] = "";
											}
										}
									}
								}
							}
						}
					}
				}
			}
		} catch (FileNotFoundException e) {
			throw new RuntimeException("文件未找到");
		} catch (IOException e) {
			throw new RuntimeException("文件解析异常");
		} catch (Exception e) {
			throw new RuntimeException();
		} finally {
			try {
				workBook.close();
			} catch (Exception e) {

			}
		}
		return dataArray[0];
	}

	/**
	 * 生成表格
	 * @param file
	 * @param titles
	 * @param rows
	 */
	public static void create(File file,List<String> titles,List<List<String>> rows){
		FileOutputStream outputStream = null;
		XSSFWorkbook workbook = null;
		try {
			workbook = new XSSFWorkbook();
			outputStream = new FileOutputStream(file);
			XSSFSheet sheet = workbook.createSheet();
			// 表头样式
			final XSSFFont font = workbook.createFont();
			font.setBold(true);
//			xssfFont.setColor(IndexedColors.BLUE.getIndex());
			XSSFCellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setFont(font);
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			// 表头
			int colSize = titles.size();
			int rowSize = rows.size();
			XSSFRow first = sheet.createRow(0);
			for (int i = 0; i < colSize; i++) {
				XSSFCell cell = first.createCell(i);
				cell.setCellStyle(cellStyle);
				cell.setCellValue(titles.get(i));
			}
			// 数据
			for (int i = 0; i < rowSize; i++) {
				XSSFRow row = sheet.createRow(i + 1);
				for (int j = 0; j < colSize; j++) {
					XSSFCell cell = row.createCell(j);
					cell.setCellValue(rows.get(i).get(j));
				}
			}
			// 宽度自适应
			for (int i = 0; i < colSize; i++) {
				sheet.autoSizeColumn(i, true);
				int width = (int) (sheet.getColumnWidth(i) * 17 / 10);
				//可以根据自己想要的列宽进行修改,测试过最大设置65000正常使用
				if (width > 60000) {
					sheet.setColumnWidth(i, 60000);
				} else {
					sheet.setColumnWidth(i, width);
				}
			}
			workbook.write(outputStream);
			workbook.close();
			outputStream.flush();
			outputStream.close();
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new RuntimeException();
		} finally {
			try {
				if (workbook != null) {
					workbook.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (Exception e) {

			}
		}
	}
	/**
	 * 解析word
	 * @param
	 * @return
	 *
	 */
	public static List<XWPFParagraph> parseWord(File file){
		FileInputStream fis = null;
		XWPFDocument xdoc = null;
		try {
			fis = new FileInputStream(file);
			xdoc = new XWPFDocument(fis);
			List<XWPFParagraph> paras = xdoc.getParagraphs();
			return paras;
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			try {
				xdoc.close();
				fis.close();
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}

	/*public static String getExamName(File file){
		//File file = new File(url);
		try {
			String a = file.getName();
			String[] strData = a.split("\\.");
			String examName = strData[0];
			return examName;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}*/




}