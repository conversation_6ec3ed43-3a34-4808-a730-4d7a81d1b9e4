package com.common.util;

import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Base64Utils;

import com.common.bean.JsonWebToken;

public class EncryptUtil {
    @Value("${jwt.token.secret}")
    private static final String DEFAULT_SECRET_KEY = "aF45fGvSwD6H0mGgpdFBRJ6Kavy9Zwi3KgT5r2MVyeAebKxH3";

    private static final String SECRET_KEY = DEFAULT_SECRET_KEY.substring(0, 16);
    private static final String IV_PARAMETER = DEFAULT_SECRET_KEY.substring(16, 32);

    private static final String TOKEN_USER_ID = "userId";
    private static final String TOKEN_TYPE = "type";

    private static Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(DEFAULT_SECRET_KEY);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public static String getRandPassword() {
        StringBuilder builder = new StringBuilder();
        List<Integer> list = NumberUtil.getRandomNumber(6);
        if (list != null) {
            for (Integer integer : list) {
                builder.append(integer);
            }
        }
        return builder.toString();
    }

    public static String getCaptcha() {
        StringBuilder builder = new StringBuilder();
        List<Integer> list = NumberUtil.getRandomNumber(4);
        if (list != null) {
            for (Integer integer : list) {
                builder.append(integer);
            }
        }
        return builder.toString();
    }

    public static String encodeWithAES(String str) {
        if (!StringUtil.isEmpty(str)) {
            try {
                IvParameterSpec ivParameterSpec = new IvParameterSpec(IV_PARAMETER.getBytes(StandardCharsets.UTF_8));
                SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
                cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
                byte[] encrypted = cipher.doFinal(str.getBytes());
                return Base64Utils.encodeToString(encrypted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String decodeWithAES(String str) {
        if (!StringUtil.isEmpty(str)) {
            try {
                IvParameterSpec ivParameterSpec = new IvParameterSpec(IV_PARAMETER.getBytes(StandardCharsets.UTF_8));
                SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
                byte[] decrypted = cipher.doFinal(Base64Utils.decodeFromString(str));
                return new String(decrypted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    public static String createJwt(JsonWebToken jsonWebToken) {
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        JwtBuilder builder = Jwts.builder().setId(jsonWebToken.getId())
                .setIssuedAt(jsonWebToken.getIssuedAt())
                .setSubject(jsonWebToken.getSubject())
                .setIssuer(jsonWebToken.getIssuer())
                .setClaims(getClaims(jsonWebToken))
                .signWith(signatureAlgorithm, getSigningKey());
        if (jsonWebToken.getExpiration() != null) {
            builder.setExpiration(jsonWebToken.getExpiration());
        }
        return builder.compact();
    }

    public static JsonWebToken parseJwt(String jwt) {
        Claims claims = Jwts.parser()
                .setSigningKey(getSigningKey()).build().parseSignedClaims(jwt).getPayload();
        JsonWebToken jsonWebToken = new JsonWebToken();
        jsonWebToken.setId(claims.getId());
        jsonWebToken.setIssuer(claims.getIssuer());
        jsonWebToken.setIssuedAt(claims.getIssuedAt());
        jsonWebToken.setSubject(claims.getSubject());
        jsonWebToken.setExpiration(claims.getExpiration());
        jsonWebToken.setUserId(Long.parseLong(claims.get(TOKEN_USER_ID).toString()));
        jsonWebToken.setType(claims.get(TOKEN_TYPE).toString());
        return jsonWebToken;
    }

    private static Map<String, Object> getClaims(JsonWebToken jsonWebToken) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(TOKEN_USER_ID, jsonWebToken.getUserId());
        map.put(TOKEN_TYPE, jsonWebToken.getType());
        return map;
    }
}
