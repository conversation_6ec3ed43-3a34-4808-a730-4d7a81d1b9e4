package com.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ClientOsInfo {
	private String osTypeVersion;
	private String deviceType;
	private String osType;
	private String version;
	private String userAgent;

	public String getOsTypeVersion() {
		return osTypeVersion;
	}

	public void setOsTypeVersion(String osTypeVersion) {
		this.osTypeVersion = osTypeVersion;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getOsType() {
		return osType;
	}

	public void setOsType(String osType) {
		this.osType = osType;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public boolean isMobile() {
		return (!StringUtil.isEmpty(deviceType));
	}

	private final String OSTYPE_ANDROID = "Android";
	private final String OSTYPE_IOS = "Ios";
	private final String OSTYPE_WP = "WINDOWS PHONE";
	private final String OSTYPE_BLACKBERRY = "BLACKBERRY";
	public static final String DEVICE_TYPE_PAD = "Pad";
	public static final String DEVICE_TYPE_PHONE = "Phone";

	public boolean verifyClientVersion(String clientVersion) {
		boolean result = Pattern.matches("[\\d\\.]+", clientVersion);
		if (result) {
			result = Pattern.matches("^\\d\\.\\d\\.\\d\\.\\d$", clientVersion);
			return result;
		} else {
			return false;
		}
	}

	public String getMobModel(String UA, String operatingSystem) {
		if (UA == null) {
			return null;
		}
		String rex = "";
		// 苹果产品
		if (operatingSystem.indexOf("IOS") != -1) {
			if (UA.indexOf("IPAD") != -1) {// 判断是否为ipad
				return "IPAD";
			}
			if (UA.indexOf("IPOD") != -1) {// 判断是否为ipod
				return "IPOD";
			}
			if (UA.indexOf("IPONE") != -1) {// 判断是否为ipone
				return "IPONE";
			}
			return "IOS DEVICE";

		}
		// 安卓系统产品
		if (operatingSystem.indexOf("ANDROID") != -1) {
			String re = "BUILD";
			rex = ".*" + ";" + "(.*)" + re;
			Pattern p = Pattern.compile(rex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean rs = m.find();
			if (rs) {
				return m.group(1);
			}
		}
		return null;
	}

	/**
	 * 判断手机的操作系统 IOS/android/windows phone/BlackBerry
	 * 
	 * @param UA
	 * @return
	 */
	public ClientOsInfo getMobilOs(String UA) {
		UA = UA.toUpperCase();
		if (UA == null) {
			return null;
		}
		ClientOsInfo osInfo = new ClientOsInfo();
		// 存放正则表达式
		String rex = "";
		// IOS 判断字符串
		String iosString = " LIKE MAC OS X";
		if (UA.indexOf(iosString) != -1) {
			if (isMatch(UA, "\\([\\s]*iPhone[\\s]*;", Pattern.CASE_INSENSITIVE)) {
				osInfo.setDeviceType(DEVICE_TYPE_PHONE);
			} else if (isMatch(UA, "\\([\\s]*iPad[\\s]*;",
					Pattern.CASE_INSENSITIVE)) {
				osInfo.setDeviceType(DEVICE_TYPE_PAD);
			}
			rex = ".*" + "[\\s]+(\\d[_\\d]*)" + iosString;
			Pattern p = Pattern.compile(rex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean rs = m.find();
			if (rs) {
				String osVersion = m.group(1).replace("_", ".");
				osInfo.setVersion(osVersion);
				osInfo.setOsTypeVersion(OSTYPE_IOS + "_" + osVersion);
			} else {
				osInfo.setOsTypeVersion(OSTYPE_IOS);
			}
			osInfo.setOsType(OSTYPE_IOS);
			return osInfo;
		}
		// Android 判断
		String androidString = "ANDROID";
		if (UA.indexOf(androidString) != -1) {
			if (isMatch(UA, "\\bMobi", Pattern.CASE_INSENSITIVE)) {
				osInfo.setDeviceType(DEVICE_TYPE_PHONE);
			} else {
				osInfo.setDeviceType(DEVICE_TYPE_PAD);
			}
			rex = ".*" + androidString + "[\\s]*(\\d*[\\._\\d]*)";
			Pattern p = Pattern.compile(rex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean rs = m.find();
			if (rs) {
				String version = m.group(1).replace("_", ".");
				osInfo.setVersion(version);
				osInfo.setOsTypeVersion(OSTYPE_ANDROID + "_" + version);
			} else {
				osInfo.setOsTypeVersion(OSTYPE_ANDROID);
			}
			osInfo.setOsType(OSTYPE_ANDROID);
			return osInfo;
		}
		// windows phone 判断
		String wpString = "WINDOWS PHONE";
		if (UA.indexOf(wpString) != -1) {
			rex = ".*" + wpString + "[\\s]*[OS\\s]*([\\d][\\.\\d]*)";
			Pattern p = Pattern.compile(rex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean rs = m.find();
			if (rs) {
				String version = m.group(1);
				osInfo.setVersion(version);
				osInfo.setOsTypeVersion(OSTYPE_WP + "_" + version);
			} else {
				osInfo.setOsTypeVersion(OSTYPE_WP);
			}
			osInfo.setOsType(OSTYPE_WP);
			return osInfo;
		}
		// BlackBerry 黑莓系统判断
		String bbString = "BLACKBERRY";
		if (UA.indexOf(bbString) != -1) {
			rex = ".*" + bbString + "[\\s]*([\\d]*)";
			Pattern p = Pattern.compile(rex, Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean rs = m.find();
			if (rs) {
				String version = m.group(1);
				osInfo.setVersion(version);
				osInfo.setOsTypeVersion(OSTYPE_BLACKBERRY + "_" + version);
			} else {
				osInfo.setOsTypeVersion(OSTYPE_BLACKBERRY);
			}
			osInfo.setOsType(OSTYPE_BLACKBERRY);
			return osInfo;
		}
		if (UA.contains("LINUX")) {// android
			if (isMatch(UA, "\\bMobi", Pattern.CASE_INSENSITIVE)) {
				osInfo.setDeviceType(DEVICE_TYPE_PHONE);
			} else {
				osInfo.setDeviceType(DEVICE_TYPE_PAD);
			}
			Pattern p = Pattern.compile(
					"U;\\s*(Adr[\\s]*)?(\\d[\\.\\d]*\\d)[\\s]*;",
					Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean result = m.find();
			String findResult = null;
			if (result) {
				findResult = m.group(2);
			}
			if (StringUtil.isEmpty(findResult)) {
				osInfo.setOsTypeVersion(OSTYPE_ANDROID);
				return osInfo;
			} else {
				osInfo.setVersion(findResult);
				osInfo.setOsTypeVersion(OSTYPE_ANDROID + "_" + findResult);
				return osInfo;
			}
		}
		// UCWEB/2.0 (iOS; U; iPh OS 4_3_2; zh-CN; iPh4)
		if (UA.matches(".*((IOS)|(iPAD)).*(IPH).*")) {
			if (isMatch(UA, "[\\s]*iPh[\\s]*", Pattern.CASE_INSENSITIVE)) {
				osInfo.setDeviceType(DEVICE_TYPE_PHONE);
			} else {
				osInfo.setDeviceType(DEVICE_TYPE_PAD);
			}
			Pattern p = Pattern.compile(
					"U;\\s*(IPH[\\s]*)?(OS[\\s]*)?(\\d[\\._\\d]*\\d)[\\s]*;",
					Pattern.CASE_INSENSITIVE);
			Matcher m = p.matcher(UA);
			boolean result = m.find();
			String findResult = null;
			if (result) {
				findResult = m.group(3);
			}
			if (StringUtil.isEmpty(findResult)) {
				osInfo.setOsTypeVersion(OSTYPE_IOS);
				osInfo.setOsType(OSTYPE_IOS);
				return osInfo;
			} else {
				String version = findResult.replace("_", ".");
				osInfo.setVersion(version);
				osInfo.setOsTypeVersion(OSTYPE_IOS + "_" + version);
				osInfo.setOsType(OSTYPE_IOS);
				return osInfo;
			}
		}
		return osInfo;
	}

	private boolean isMatch(String source, String regx, int flags) {
		Pattern p = Pattern.compile(regx, flags);
		Matcher m = p.matcher(source);
		boolean result = m.find();
		return result;
	}

//	public ClientOsInfo getMobileOsInfo(HttpServletRequest request) {
//		String userAgent = request.getHeader("user-agent");
//		if (StringUtil.isNullStr(userAgent)) {
//			userAgent = request.getHeader("User-Agent");
//		}
//		ClientOsInfo info = this.getMobilOs(userAgent);
//		info.setUserAgent(userAgent);
//		return info;
//	}
}