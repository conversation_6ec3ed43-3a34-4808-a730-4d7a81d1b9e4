package com.common.client;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URI;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.common.bean.Response;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

public class FileSystemClient<T> {
	
	private final String multiKey = "file";
	
	public Response<T> upload(String url, File file, Class<?> clazz){
		CloseableHttpClient httpClient = null;
		try {			
			httpClient = HttpClients.custom()
		        .setSSLHostnameVerifier(new NoopHostnameVerifier())
		        .build();
		    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		    requestFactory.setHttpClient(httpClient);
		    MultiValueMap<String, Object> multiPartBody = new LinkedMultiValueMap<>();
		    multiPartBody.add(multiKey, new FileSystemResource(file));
			RequestEntity<?> requestEntity = RequestEntity.post(new URI(url))
					.header(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORM_DATA_VALUE)
					.body(multiPartBody);
			RestTemplate restTemplate = new RestTemplate(requestFactory);
//			restTemplate.getMessageConverters().add(new ByteArrayHttpMessageConverter());
//			restTemplate.getMessageConverters().add(new ResourceHttpMessageConverter());
			ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity,String.class);
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
			return objectMapper.readValue(responseEntity.getBody(), this.getJavaType(objectMapper, clazz));
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			try {
				httpClient.close();
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}
	
	public void download(String url,File file){
		CloseableHttpClient httpClient = null;
		try {			
			httpClient = HttpClients.custom()
		        .setSSLHostnameVerifier(new NoopHostnameVerifier())
		        .build();
		    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		    requestFactory.setHttpClient(httpClient);
			RequestEntity<?> requestEntity = RequestEntity.get(new URI(url)).build();
			RestTemplate restTemplate = new RestTemplate(requestFactory);
			ResponseEntity<byte[]> responseEntity = restTemplate.exchange(requestEntity,byte[].class);
			FileOutputStream fos = new FileOutputStream(file);
			fos.write(responseEntity.getBody());
			fos.flush();
			fos.close();
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			try {
				httpClient.close();
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
	}
	
	private JavaType getJavaType(ObjectMapper mapper, Class<?>... clazz){
		 return mapper.getTypeFactory().constructParametricType(Response.class, clazz);
	}
}
