package com.common.client;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import com.common.constant.App;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class TiandituClient<T> {


    @Value("${tianditu.token}")
    private static String SECRET;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private HttpMethod method = HttpMethod.POST;
    private final int timeout = 30 * 1000;

    private static final String SERVICE_URL = "api.tianditu.gov.cn";

    public T execute(String path, Object request, Class<?> clazz) {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            URI uri = new URI("https", SERVICE_URL, path, "tk=" + SECRET, null);
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            requestFactory.setConnectionRequestTimeout(timeout);
            requestFactory.setConnectTimeout(timeout);
            requestFactory.setReadTimeout(timeout);
            RequestEntity<?> requestEntity;
            switch (method) {
                case GET:
                    requestEntity = RequestEntity.get(uri)
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case PUT:
                    requestEntity = RequestEntity.put(uri)
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
                case DELETE:
                    requestEntity = RequestEntity.delete(uri)
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case POST:
                default:
                    requestEntity = RequestEntity.post(uri)
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
            logger.info("tianditu service request : {}", objectMapper.writeValueAsString(requestEntity));
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            restTemplate.setMessageConverters(this.getMessageConverters(restTemplate.getMessageConverters()));
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
            logger.info("tianditu service response : {}", responseEntity);
            return objectMapper.readValue(responseEntity.getBody(), objectMapper.getTypeFactory().constructType(clazz));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }


    public HttpMethod getMethod() {
        return method;
    }

    public void setMethod(HttpMethod method) {
        this.method = method;
    }

    /**
     * 转换StringHttpMessageConverter默认编码问题
     *
     * @param converterList 要转换的 HttpMessageConverter 列表
     * @return 转换后的 HttpMessageConverter 列表
     */
    private List<HttpMessageConverter<?>> getMessageConverters(List<HttpMessageConverter<?>> converterList) {
        List<HttpMessageConverter<?>> list = new ArrayList<>();
        for (HttpMessageConverter<?> element : converterList) {
            if (element instanceof StringHttpMessageConverter) {
                list.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
            } else {
                list.add(element);
            }
        }
        return list;
    }
}
