package com.common.constant;

import java.util.HashMap;
import java.util.Map;

public enum Platform {
	WECHAT("wechat","微信小程序"),
	PC("sys","管理员后台");
	public static final Map<String, Device> DEVIVCE = new HashMap<String, Device>();
	static {
		DEVIVCE.put(WECHAT.code, Device.MD);
		DEVIVCE.put(PC.code, Device.PC);
	}
	
	private String code;
	private String name;
	private Platform(String code,String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (Platform gender : Platform.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (Platform gender : Platform.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
	public static Platform get(String code){
		for (Platform value : Platform.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
		return null;
	}
}
