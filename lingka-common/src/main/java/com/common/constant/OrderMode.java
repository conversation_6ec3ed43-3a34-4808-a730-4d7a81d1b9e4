package com.common.constant;

public enum OrderMode {
	TICKETS("tickets","门票制","固定门票价格，包含单点值内容"),
	DIRECT("direct","单点制","按照实际消费点单收费");
	private String code;
	private String name;
	private String description;
	private OrderMode(String code, String name,String description){
		this.code = code;
		this.name = name;
		this.description = description;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public static String getCode(String name){
		for (OrderMode gender : OrderMode.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderMode gender : OrderMode.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
}
