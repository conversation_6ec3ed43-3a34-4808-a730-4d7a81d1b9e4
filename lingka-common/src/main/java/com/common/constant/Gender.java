package com.common.constant;

public enum Gender {
	MALE("0","男"),
	FEMALE("1","女");
	private String code;
	private String name;
	private Gender(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (Gender gender : Gender.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (Gender gender : Gender.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
}
