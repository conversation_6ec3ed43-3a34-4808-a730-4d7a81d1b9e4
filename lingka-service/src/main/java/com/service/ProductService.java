package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ProductIngredient;
import com.domain.ProductIngredientConfig;
import com.domain.ProductOption;
import com.domain.ProductSku;
import com.github.pagehelper.PageInfo;
import com.domain.Product;
import com.dto.ProductDTO;
import com.query.ProductQuery;

/**
 * 产品Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductService {
    /**
     * 查询产品
     * 
     * @param id 产品ID
     * @return 产品
     */
    ProductDTO findById(Long id);

    /**
     * 查询产品列表
     *
     * @param ids 编号集合
     * @return 产品集合
     */
    List<ProductDTO> findByIds(List<Long> ids);

    /**
     * 查询产品列表
     * 
     * @param productQuery 产品
     * @return 产品集合
     */
    List<ProductDTO> findAll(ProductQuery productQuery);

	/**
	 *  分页查询产品列表
	 *
	 * @param productQuery 产品
	 * @return 产品集合
	 */
	PageInfo<ProductDTO> find(ProductQuery productQuery);

    /**
     * 查询产品Map
     *
     * @param ids 编号集合
     * @return 产品Map
     */
    Map<Long, ProductDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品
     * 
     * @param product 产品
     * @return 结果
     */
    int create(Product product);


    /**
     * 新增产品和SKU
     *
     * @param product 产品
     * @param productSkus 产品SKU
     * @return 结果
     */
    void createAndSku(Product product,List<ProductSku> productSkus);

    /**
     * 新增产品和SKU
     *
     * @param product 产品
     * @param productSkus 产品SKU
     * @return 结果
     */
    void createAndSkuAndIngredientAndConfig(Product product, List<ProductSku> productSkus, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig);

    void createAndSkuAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions);

    void createAndSkuAndIngredientAndConfigAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig);

    /**
     * 新增产品和SKU
     *
     * @param product 产品
     * @param productSkus 产品SKU
     * @return 结果
     */
    void modifyAndSku(Product product,List<ProductSku> productSkus);

    /**
     * 新增产品和SKU
     *
     * @param product 产品
     * @param productSkus 产品SKU
     * @return 结果
     */
    void modifyAndSkuAndIngredientAndConfig(Product product, List<ProductSku> productSkus, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig);

    void modifyAndSkuAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions);

    void modifyAndSkuAndIngredientAndConfigAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig);

    /**
     * 修改产品
     * 
     * @param product 产品
     * @return 结果
     */
    int modifyById(Product product);

    /**
     * 删除产品信息
     * 
     * @param id 产品id
     * @return 结果
     */
   int removeById(Long id);
}
