package com.service.impl;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.dao.ShopConfigDao;
import com.dao.UserShopHistoryDao;
import com.domain.UserShopHistory;
import com.dto.ShopConfigDTO;
import com.dto.UserShopHistoryDTO;
import com.github.pagehelper.Page;
import com.query.UserShopHistoryQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserShopHistoryService;

/**
 * 用户浏览店铺历史记录Service业务层处理
 *
 */
@Service
public class UserShopHistoryServiceImpl extends BaseService implements UserShopHistoryService {

    @Autowired
    private UserShopHistoryDao userShopHistoryDao;
    @Autowired
    private ShopConfigDao shopConfigDao;

    /**
     * 查询用户浏览店铺历史记录
     *
     * @param id 用户浏览店铺历史记录ID
     * @return 用户浏览店铺历史记录
     */
    @Override
    public UserShopHistoryDTO findById(Long id) {
        return userShopHistoryDao.selectById(id);
    }

    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param ids 编号集合
     * @return 用户浏览店铺历史记录集合
     */
    @Override
    public List<UserShopHistoryDTO> findByIds(List<Long> ids) {
        return userShopHistoryDao.selectByIds(ids);
    }

    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param userShopHistoryQuery 用户浏览店铺历史记录
     * @return 用户浏览店铺历史记录
     */
    @Override
    public List<UserShopHistoryDTO> findAll(UserShopHistoryQuery userShopHistoryQuery) {
        List<UserShopHistoryDTO> userShopHistoryDTOList = userShopHistoryDao.select(userShopHistoryQuery);
        Iterator<UserShopHistoryDTO> iterator = userShopHistoryDTOList.iterator();
        while (iterator.hasNext()) {
            UserShopHistoryDTO it = iterator.next();
            ShopConfigDTO shopConfigDTO = shopConfigDao.selectByShopId(it.getShopId());
            if (shopConfigDTO != null && DataStatus.Y.getCode().equals(shopConfigDTO.getStatus())) {
                it.setShopConfig(shopConfigDTO);
            } else {
                if (shopConfigDTO != null) {
                    it.setStatus(DataStatus.N.getCode());
                    UserShopHistory userShopHistory = new UserShopHistory();
                    BeanUtils.copyProperties(it, userShopHistory);
                    userShopHistoryDao.updateById(userShopHistory);
                }
                iterator.remove(); // 用 iterator 安全删除
            }
        }
        return userShopHistoryDTOList;
    }

    /**
     * 分页查询用户浏览店铺历史记录列表
     *
     * @param userShopHistoryQuery 用户浏览店铺历史记录
     * @return 用户浏览店铺历史记录
     */
    @Override
    public PageInfo<UserShopHistoryDTO> find(UserShopHistoryQuery userShopHistoryQuery) {
        try (Page<UserShopHistoryDTO> page = PageHelper.startPage(userShopHistoryQuery.getPageNum(), userShopHistoryQuery.getPageSize())) {
            userShopHistoryDao.select(userShopHistoryQuery);
            Iterator<UserShopHistoryDTO> iterator = page.iterator();
            while (iterator.hasNext()) {
                UserShopHistoryDTO it = iterator.next();
                ShopConfigDTO shopConfigDTO = shopConfigDao.selectByShopId(it.getShopId());
                if (shopConfigDTO != null && DataStatus.Y.getCode().equals(shopConfigDTO.getStatus())) {
                    it.setShopConfig(shopConfigDTO);
                } else {
                    if (shopConfigDTO != null) {
                        it.setStatus(DataStatus.N.getCode());
                        UserShopHistory userShopHistory = new UserShopHistory();
                        BeanUtils.copyProperties(it, userShopHistory);
                        userShopHistoryDao.updateById(userShopHistory);
                    }
                    iterator.remove(); // 安全删除
                }
            }
            return page.toPageInfo();
        }
    }

    /**
     * 查询用户浏览店铺历史记录Map
     *
     * @param ids 编号集合
     * @return 用户浏览店铺历史记录Map
     */
    @Override
    public Map<Long, UserShopHistoryDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserShopHistoryDTO> userShopHistoryDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<UserShopHistoryDTO> userShopHistoryDTOList = userShopHistoryDao.selectByIds(ids);
            for (UserShopHistoryDTO userShopHistoryDTO : userShopHistoryDTOList) {
                userShopHistoryDTOMap.put(userShopHistoryDTO.getId(), userShopHistoryDTO);
            }
        }
        return userShopHistoryDTOMap;
    }

    /**
     * 新增用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    @Override
    public int create(UserShopHistory userShopHistory) {
        return userShopHistoryDao.insert(userShopHistory);
    }

    /**
     * 修改用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    @Override
    public int modifyById(UserShopHistory userShopHistory) {
        return userShopHistoryDao.updateById(userShopHistory);
    }


    /**
     * 删除用户浏览店铺历史记录信息
     *
     * @param id 用户浏览店铺历史记录ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userShopHistoryDao.deleteById(id);
    }

    @Override
    public int removeByShopId(Long shopId) {
        return userShopHistoryDao.deleteByShopId(shopId);
    }

}
