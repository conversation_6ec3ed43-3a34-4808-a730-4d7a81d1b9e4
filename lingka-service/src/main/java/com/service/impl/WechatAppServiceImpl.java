package com.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.common.bean.WechatAccessTokenResponse;
import com.common.client.WechatClient;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.dao.WechatAppDao;
import com.domain.WechatApp;
import com.dto.WechatAppDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.query.WechatAppQuery;
import com.service.WechatAppService;

/**
 * 微信应用Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WechatAppServiceImpl extends BaseService implements WechatAppService {

    @Autowired
    private WechatAppDao wechatAppDao;

    /**
     * 查询微信应用
     * 
     * @param id 微信应用ID
     * @return 微信应用
     */
    @Override
    public WechatAppDTO findById(Long id) {
        WechatAppDTO wechatAppDTO = this.wechatAppDao.selectById(id);
        Date datetime = this.getServerTime();
        if (wechatAppDTO != null) {
            // 未过期,直接返回
            if (!this.isEmpty(wechatAppDTO.getAccessToken())
                    && !this.isEmpty(wechatAppDTO.getAccessTokenDatetime())
                    && datetime.before(DateUtil.getFutureMinute(wechatAppDTO.getAccessTokenDatetime(), 90))
            ) {
                return wechatAppDTO;
            }
            // 刷新
            try {
                StringBuilder loginBuilder = new StringBuilder("/cgi-bin/token?");
                loginBuilder.append("appid=");
                loginBuilder.append(wechatAppDTO.getAppid());
                loginBuilder.append("&secret=");
                loginBuilder.append(wechatAppDTO.getSecret());
                loginBuilder.append("&grant_type=client_credential");
                WechatClient<WechatAccessTokenResponse> client = new WechatClient<>();
                client.setMethod(HttpMethod.GET);
                WechatAccessTokenResponse wechatAccessTokenResponse = client.execute(loginBuilder.toString(),null,WechatAccessTokenResponse.class);
                if (wechatAccessTokenResponse.getErrcode() != null && wechatAccessTokenResponse.getErrcode() != 0){
                    return wechatAppDTO;
                }
                logger.info("wechat access token : {}", wechatAccessTokenResponse.getAccessToken());
                // 更新到数据库
                WechatApp wechatAppModify = new WechatApp();
                wechatAppModify.setId(wechatAppDTO.getId());
                wechatAppModify.setAccessToken(wechatAccessTokenResponse.getAccessToken());
                wechatAppModify.setAccessTokenDatetime(datetime);
                wechatAppModify.setModifyTime(datetime);
                this.wechatAppDao.updateById(wechatAppModify);
                wechatAppDTO.setAccessToken(wechatAccessTokenResponse.getAccessToken());
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
                throw new RuntimeException(e);
            }
        }
        return wechatAppDTO;
    }

    /**
     * 查询微信应用列表
     *
     * @param wechatAppDTO 微信应用
     * @return 微信应用
     */
    @Override
    public List<WechatAppDTO> findAll(WechatAppQuery wechatAppDTO) {
        wechatAppDTO.setStatus(DataStatus.Y.getCode());
        return wechatAppDao.select(wechatAppDTO);
    }

    /**
     * 分页查询微信应用列表
     *
     * @param wechatAppDTO 微信应用
     * @return 微信应用
     */
    @Override
    public PageInfo<WechatAppDTO> find(WechatAppQuery wechatAppDTO) {
        wechatAppDTO.setStatus(DataStatus.Y.getCode());
        PageHelper.startPage(wechatAppDTO.getPageNum(),wechatAppDTO.getPageSize());
        List<WechatAppDTO> wechatAppDTOs = wechatAppDao.select(wechatAppDTO);
        return new PageInfo<>(wechatAppDTOs);
    }

    /**
     * 新增微信应用
     *
     * @param wechatApp 微信应用
     * @return 结果
     */
    @Override
    public int create(WechatApp wechatApp) {
        Date datetime = this.getServerTime();
        wechatApp.setStatus(DataStatus.Y.getCode());
        wechatApp.setModifyTime(datetime);
        wechatApp.setCreateTime(datetime);
        return wechatAppDao.insert(wechatApp);
    }

    /**
     * 修改微信应用
     *
     * @param wechatApp 微信应用
     * @return 结果
     */
    @Override
    public int modifyById(WechatApp wechatApp) {
        Date datetime = this.getServerTime();
        wechatApp.setModifyTime(datetime);
        return wechatAppDao.updateById(wechatApp);
    }


    /**
     * 删除微信应用信息
     *
     * @param id 微信应用ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return wechatAppDao.deleteById(id);
    }

}
