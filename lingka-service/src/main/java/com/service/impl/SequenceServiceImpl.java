package com.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.dao.SequenceDao;
import com.domain.Sequence;
import com.dto.SequenceDTO;
import com.service.BaseService;
import com.service.SequenceService;

@Service
public class SequenceServiceImpl extends BaseService implements SequenceService {
	
	@Autowired private SequenceDao sequenceDao;
	
	@Override
	public SequenceDTO findById(Long id) {
		return this.sequenceDao.selectById(id);
	}

	@Override
	public Integer create(Sequence sequence) {
		Date datetime = DateUtil.getServerTime();
		sequence.setStatus(DataStatus.Y.getCode());
		sequence.setModifyTime(datetime);
		sequence.setCreateTime(datetime);
		return this.sequenceDao.insert(sequence);
	}

}
