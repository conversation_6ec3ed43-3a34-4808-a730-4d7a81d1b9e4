package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserRoleDao;
import com.dao.UserShopDao;
import com.domain.UserRole;
import com.domain.UserShop;
import com.dto.UserShopDTO;
import com.query.UserShopQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserShopService;

/**
 * 用户店铺关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class UserShopServiceImpl extends BaseService implements UserShopService {

    @Autowired
    private UserShopDao userShopDao;
    @Autowired
    private UserRoleDao userRoleDao;

    /**
     * 查询用户店铺关联
     * 
     * @param id 用户店铺关联ID
     * @return 用户店铺关联
     */
    @Override
    public UserShopDTO findById(Long id) {
        return userShopDao.selectById(id);
    }

    /**
     * 查询用户店铺关联列表
     *
     * @param ids 编号集合
     * @return 用户店铺关联集合
     */
    @Override
    public List<UserShopDTO> findByIds(List<Long> ids) {
        return userShopDao.selectByIds(ids);
    }

    /**
     * 查询用户店铺关联列表
     *
     * @param userShopQuery 用户店铺关联
     * @return 用户店铺关联
     */
    @Override
    public List<UserShopDTO> findAll(UserShopQuery userShopQuery) {
        return userShopDao.select(userShopQuery);
    }

	/**
	 * 分页查询用户店铺关联列表
	 *
	 * @param userShopQuery 用户店铺关联
	 * @return 用户店铺关联
	 */
	@Override
	public PageInfo<UserShopDTO> find(UserShopQuery userShopQuery) {
        PageHelper.startPage(userShopQuery.getPageNum(),userShopQuery.getPageSize());
		List<UserShopDTO> userShopDTOList = userShopDao.select(userShopQuery);
		return new PageInfo<>(userShopDTOList);
	}

    /**
     * 查询用户店铺关联Map
     *
     * @param ids 编号集合
     * @return 用户店铺关联Map
     */
    @Override
    public Map<Long, UserShopDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserShopDTO> userShopDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserShopDTO> userShopDTOList =  userShopDao.selectByIds(ids);
            for (UserShopDTO userShopDTO : userShopDTOList) {
                    userShopDTOMap.put(userShopDTO.getId(),userShopDTO);
            }
        }
        return userShopDTOMap;
    }

    /**
     * 新增用户店铺关联
     *
     * @param userShop 用户店铺关联
     * @return 结果
     */
    @Override
    public int create(UserShop userShop) {
        return userShopDao.insert(userShop);
    }

    @Override
    @Transactional
    public int createAndUserRoles(UserShop userShop, List<UserRole> userRoles) {
        for (UserRole userRole : userRoles) {
            userRoleDao.insert(userRole);
        }
        return userShopDao.insert(userShop);
    }

    /**
     * 修改用户店铺关联
     *
     * @param userShop 用户店铺关联
     * @return 结果
     */
    @Override
    public int modifyById(UserShop userShop) {
        return userShopDao.updateById(userShop);
    }


    /**
     * 删除用户店铺关联信息
     *
     * @param id 用户店铺关联ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userShopDao.deleteById(id);
    }

    @Override
    @Transactional
    public int removeById(Long id, List<Long> userRoleIds) {
        for (Long userRoleId : userRoleIds) {
            userRoleDao.deleteById(userRoleId);
        }
        return userShopDao.deleteById(id);
    }

}
