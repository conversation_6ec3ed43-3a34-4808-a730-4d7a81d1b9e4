package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.common.constant.ShopConfigModifyAudit;
import com.dao.ShopConfigDao;
import com.dao.ShopConfigModifyApplyDao;
import com.domain.ShopConfig;
import com.domain.ShopConfigModifyApply;
import com.dto.ShopConfigDTO;
import com.dto.ShopConfigModifyApplyDTO;
import com.github.pagehelper.Page;
import com.query.ShopConfigModifyApplyQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ShopConfigModifyApplyService;

/**
 * 店铺配置修改申请Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShopConfigModifyApplyServiceImpl extends BaseService implements ShopConfigModifyApplyService {

    @Autowired
    private ShopConfigModifyApplyDao shopConfigModifyApplyDao;

    @Autowired
    private ShopConfigDao shopConfigDao;

    /**
     * 查询店铺配置修改申请
     *
     * @param id 店铺配置修改申请ID
     * @return 店铺配置修改申请
     */
    @Override
    public ShopConfigModifyApplyDTO findById(Long id) {
        return shopConfigModifyApplyDao.selectById(id);
    }

    /**
     * 查询店铺配置修改申请列表
     *
     * @param ids 编号集合
     * @return 店铺配置修改申请集合
     */
    @Override
    public List<ShopConfigModifyApplyDTO> findByIds(List<Long> ids) {
        return shopConfigModifyApplyDao.selectByIds(ids);
    }

    /**
     * 通过店铺 ID 查询店铺配置修改申请列表
     *
     * @param id 店铺编号
     * @return 店铺配置修改申请集合
     */
    @Override
    public List<ShopConfigModifyApplyDTO> findByShopId(Long id) {
        return shopConfigModifyApplyDao.selectByShopId(id);
    }

    /**
     * 查询店铺配置修改申请列表
     *
     * @param shopConfigModifyApplyQuery 店铺配置修改申请
     * @return 店铺配置修改申请
     */
    @Override
    public List<ShopConfigModifyApplyDTO> findAll(ShopConfigModifyApplyQuery shopConfigModifyApplyQuery) {
        return shopConfigModifyApplyDao.select(shopConfigModifyApplyQuery);
    }

    /**
     * 分页查询店铺配置修改申请列表
     *
     * @param shopConfigModifyApplyQuery 店铺配置修改申请
     * @return 店铺配置修改申请
     */
    @Override
    public PageInfo<ShopConfigModifyApplyDTO> find(ShopConfigModifyApplyQuery shopConfigModifyApplyQuery) {
        try (Page<ShopConfigModifyApplyDTO> page = PageHelper.startPage(shopConfigModifyApplyQuery.getPageNum(), shopConfigModifyApplyQuery.getPageSize())) {
            shopConfigModifyApplyDao.select(shopConfigModifyApplyQuery);
            return page.toPageInfo();
        }
    }

    /**
     * 查询店铺配置修改申请Map
     *
     * @param ids 编号集合
     * @return 店铺配置修改申请Map
     */
    @Override
    public Map<Long, ShopConfigModifyApplyDTO> findMapByIds(List<Long> ids) {
        Map<Long, ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOList = shopConfigModifyApplyDao.selectByIds(ids);
            for (ShopConfigModifyApplyDTO shopConfigModifyApplyDTO : shopConfigModifyApplyDTOList) {
                shopConfigModifyApplyDTOMap.put(shopConfigModifyApplyDTO.getId(), shopConfigModifyApplyDTO);
            }
        }
        return shopConfigModifyApplyDTOMap;
    }

    /**
     * 新增店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    @Override
    public int create(ShopConfigModifyApply shopConfigModifyApply) {
        if (shopConfigModifyApply.getPhoto() == null) {
            // 舍弃旧的审核中的文本更改
            List<ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOList = shopConfigModifyApplyDao.selectByShopId(shopConfigModifyApply.getShopId());
            for (ShopConfigModifyApplyDTO shopConfigModifyApplyDTO : shopConfigModifyApplyDTOList) {
                if (DataStatus.Y.getCode().equals(shopConfigModifyApplyDTO.getStatus())
                        && ShopConfigModifyAudit.PENDING.getCode().equals(shopConfigModifyApplyDTO.getAudit())
                        && shopConfigModifyApplyDTO.getPhoto() == null) {
                    ShopConfigModifyApply shopConfigModifyApplyOld = new ShopConfigModifyApply(shopConfigModifyApplyDTO);
                    shopConfigModifyApplyOld.setAudit(ShopConfigModifyAudit.AUTO_INVALID.getCode());
                    shopConfigModifyApplyOld.setModifyTime(getServerTime());
                    shopConfigModifyApplyDao.updateById(shopConfigModifyApplyOld);
                }
            }
        }
        shopConfigModifyApply.setStatus(DataStatus.Y.getCode());
        shopConfigModifyApply.setAudit(ShopConfigModifyAudit.PENDING.getCode());
        return shopConfigModifyApplyDao.insert(shopConfigModifyApply);
    }

    /**
     * 修改店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    @Override
    public int modifyById(ShopConfigModifyApply shopConfigModifyApply) {
        int count = shopConfigModifyApplyDao.updateById(shopConfigModifyApply);
        if (ShopConfigModifyAudit.APPROVED.getCode().equals(shopConfigModifyApply.getAudit())) {
            ShopConfig shopConfig;
            if (shopConfigModifyApply.getPhoto() != null) {
                String changeFragment = shopConfigModifyApply.getPhoto();
                String[] splitFragments = changeFragment.split(",");
                Long id = Long.parseLong(splitFragments[0]);
                id = id == 0L ? shopConfigModifyApply.getId() : id;
                int length = Integer.parseInt(splitFragments[1]);
                if (length == 1) {
                    shopConfig = new ShopConfig(shopConfigModifyApply);
                    shopConfig.setPhoto(splitFragments[3]);
                } else {
                    String[] photos = {StringUtils.repeat("2025/08/22/00/30/19027710000000.png", length)}; // TODO: 审核中占位符，需要替换为可配置位置，或者内置到前端，而不是硬编码
                    List<ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOList = shopConfigModifyApplyDao.selectByShopId(shopConfigModifyApply.getShopId());
                    for (ShopConfigModifyApplyDTO shopConfigModifyApplyDTO : shopConfigModifyApplyDTOList) {
                        if (DataStatus.Y.getCode().equals(shopConfigModifyApplyDTO.getStatus())
                                && ShopConfigModifyAudit.APPROVED.getCode().equals(shopConfigModifyApplyDTO.getAudit())
                                && shopConfigModifyApplyDTO.getPhoto() != null) {
                            String photo = shopConfigModifyApplyDTO.getPhoto();
                            String[] split = photo.split(",");
                            Long idx = Long.parseLong(split[0]);
                            idx = idx == 0L ? shopConfigModifyApplyDTO.getId() : idx;
                            if (Objects.equals(idx, id)) {
                                int index = Integer.parseInt(split[2]);
                                String url = split[3];
                                photos[index] = url;
                            }
                        }
                    }
                    String photo = String.join(",", photos);
                    shopConfig = new ShopConfig(shopConfigModifyApply);
                    shopConfig.setPhoto(photo);
                }
            } else {
                shopConfig = new ShopConfig(shopConfigModifyApply);
            }
            if (shopConfig.getId() == null) {
                ShopConfigDTO shopConfigDTO = shopConfigDao.selectByShopId(shopConfig.getShopId());
                shopConfig.setId(shopConfigDTO.getId());
            }
            shopConfig.setModifyTime(getServerTime());
            count += shopConfigDao.updateById(shopConfig);
        }
        return count;
    }


    /**
     * 删除店铺配置修改申请信息
     *
     * @param id 店铺配置修改申请ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return shopConfigModifyApplyDao.deleteById(id);
    }

    /**
     * 批量删除店铺配置修改申请信息
     *
     * @param ids 店铺配置修改申请ID列表
     * @return 删除数量
     */
    @Override
    public int removeByIds(List<Long> ids) {
        return shopConfigModifyApplyDao.deleteByIds(ids);
    }

}
