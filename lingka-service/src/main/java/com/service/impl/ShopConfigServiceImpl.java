package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.dao.ShopConfigDao;
import com.domain.ShopConfig;
import com.dto.ShopConfigDTO;
import com.github.pagehelper.Page;
import com.query.ShopConfigQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ShopConfigService;

/**
 * 店铺配置Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ShopConfigServiceImpl extends BaseService implements ShopConfigService {

    @Autowired
    private ShopConfigDao shopConfigDao;

    /**
     * 查询店铺配置
     *
     * @param id 店铺配置ID
     * @return 店铺配置
     */
    @Override
    public ShopConfigDTO findById(Long id) {
        return shopConfigDao.selectById(id);
    }

    /**
     * 查询店铺配置列表
     *
     * @param ids 编号集合
     * @return 店铺配置集合
     */
    @Override
    public List<ShopConfigDTO> findByIds(List<Long> ids) {
        return shopConfigDao.selectByIds(ids);
    }

    /**
     * 查询店铺配置列表
     *
     * @param shopConfigQuery 店铺配置
     * @return 店铺配置
     */
    @Override
    public List<ShopConfigDTO> findAll(ShopConfigQuery shopConfigQuery) {
        return shopConfigDao.select(shopConfigQuery);
    }

    /**
     * 查询店铺配置列表
     *
     * @param shopIds 店铺 ID 集合
     * @return 店铺配置集合
     */
    @Override
    public List<ShopConfigDTO> findAllByShopIds(List<Long> shopIds) {
        return shopConfigDao.selectByShopIds(shopIds);
    }

    /**
     * 分页查询店铺配置列表
     *
     * @param shopConfigQuery 店铺配置
     * @return 店铺配置
     */
    @Override
    public PageInfo<ShopConfigDTO> find(ShopConfigQuery shopConfigQuery) {
        try (Page<ShopConfigDTO> page = PageHelper.startPage(shopConfigQuery.getPageNum(), shopConfigQuery.getPageSize())) {
            shopConfigDao.select(shopConfigQuery);
            return page.toPageInfo();
        }
    }

    /**
     * 查询店铺配置Map
     *
     * @param ids 编号集合
     * @return 店铺配置Map
     */
    @Override
    public Map<Long, ShopConfigDTO> findMapByIds(List<Long> ids) {
        Map<Long, ShopConfigDTO> shopConfigDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<ShopConfigDTO> shopConfigDTOList = shopConfigDao.selectByIds(ids);
            for (ShopConfigDTO shopConfigDTO : shopConfigDTOList) {
                shopConfigDTOMap.put(shopConfigDTO.getId(), shopConfigDTO);
            }
        }
        return shopConfigDTOMap;
    }

    @Override
    public Map<Long, ShopConfigDTO> findMapByShopIds(List<Long> shopIds) {
        Map<Long, ShopConfigDTO> shopIdConfigDTOMap = new HashMap<>();
        if (shopIds != null && !shopIds.isEmpty()) {
            List<ShopConfigDTO> shopConfigDTOList = shopConfigDao.selectByShopIds(shopIds);
            for (ShopConfigDTO shopConfigDTO : shopConfigDTOList) {
                if (DataStatus.Y.getCode().equals(shopConfigDTO.getStatus()))
                    shopIdConfigDTOMap.put(shopConfigDTO.getShopId(), shopConfigDTO);
            }
        }
        return shopIdConfigDTOMap;
    }

    @Override
    public ShopConfigDTO findByShopId(Long shopId) {
        return shopConfigDao.selectByShopId(shopId);
    }

    /**
     * 新增店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    @Override
    public int create(ShopConfig shopConfig) {
        shopConfig.setStatus(DataStatus.Y.getCode());
        return shopConfigDao.insert(shopConfig);
    }

    /**
     * 修改店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    @Override
    public int modifyById(ShopConfig shopConfig) {
        return shopConfigDao.updateById(shopConfig);
    }

    /**
     * 修改店铺配置
     *
     * @param params 店铺差异配置
     * @return 更新数量
     */
    @Override
    public int updatePartialById(Map<String, Object> params) {
        return shopConfigDao.updatePartialById(params);
    }

    /**
     * 删除店铺配置信息
     *
     * @param id 店铺配置ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        ShopConfig shopConfig = new ShopConfig();
        shopConfig.setId(id);
        shopConfig.setStatus(DataStatus.N.getCode());
        return shopConfigDao.updateById(shopConfig);
    }

}
