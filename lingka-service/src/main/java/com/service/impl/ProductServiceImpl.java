package com.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.constant.DataStatus;
import com.dao.ProductDao;
import com.dao.ProductIngredientConfigDao;
import com.dao.ProductIngredientDao;
import com.dao.ProductOptionDao;
import com.dao.ProductSkuDao;
import com.domain.Product;
import com.domain.ProductIngredient;
import com.domain.ProductIngredientConfig;
import com.domain.ProductOption;
import com.domain.ProductSku;
import com.dto.ProductDTO;
import com.dto.ProductIngredientConfigDTO;
import com.dto.ProductIngredientDTO;
import com.dto.ProductOptionDTO;
import com.dto.ProductSkuDTO;
import com.query.ProductIngredientConfigQuery;
import com.query.ProductIngredientQuery;
import com.query.ProductOptionQuery;
import com.query.ProductQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.query.ProductSkuQuery;
import com.service.ProductService;

/**
 * 产品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductServiceImpl extends BaseService implements ProductService {

    @Autowired
    private ProductDao productDao;
    @Autowired
    private ProductSkuDao productSkuDao;
    @Autowired
    private ProductOptionDao productOptionDao;
    @Autowired
    private ProductIngredientDao productIngredientDao;
    @Autowired
    private ProductIngredientConfigDao productIngredientConfigDao;

    /**
     * 查询产品
     * 
     * @param id 产品ID
     * @return 产品
     */
    @Override
    public ProductDTO findById(Long id) {
        return productDao.selectById(id);
    }

    /**
     * 查询产品列表
     *
     * @param ids 编号集合
     * @return 产品集合
     */
    @Override
    public List<ProductDTO> findByIds(List<Long> ids) {
        return productDao.selectByIds(ids);
    }

    /**
     * 查询产品列表
     *
     * @param productQuery 产品
     * @return 产品
     */
    @Override
    public List<ProductDTO> findAll(ProductQuery productQuery) {
        return productDao.select(productQuery);
    }

	/**
	 * 分页查询产品列表
	 *
	 * @param productQuery 产品
	 * @return 产品
	 */
	@Override
	public PageInfo<ProductDTO> find(ProductQuery productQuery) {
        PageHelper.startPage(productQuery.getPageNum(),productQuery.getPageSize());
		List<ProductDTO> productDTOList = productDao.select(productQuery);
		return new PageInfo<>(productDTOList);
	}

    /**
     * 查询产品Map
     *
     * @param ids 编号集合
     * @return 产品Map
     */
    @Override
    public Map<Long, ProductDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductDTO> productDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductDTO> productDTOList =  productDao.selectByIds(ids);
            for (ProductDTO productDTO : productDTOList) {
                    productDTOMap.put(productDTO.getId(),productDTO);
            }
        }
        return productDTOMap;
    }

    /**
     * 新增产品
     *
     * @param product 产品
     * @return 结果
     */
    @Override
    public int create(Product product) {
        return productDao.insert(product);
    }

    @Override
    @Transactional
    public void createAndSku(Product product, List<ProductSku> productSkus) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
    }

    @Override
    @Transactional
    public void createAndSkuAndIngredientAndConfig(Product product, List<ProductSku> productSkus, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
        for (ProductIngredient productIngredient : productIngredients) {
            productIngredient.setProductId(product.getId());
            productIngredientDao.insert(productIngredient);
        }
        productIngredientConfig.setProductId(product.getId());
        productIngredientConfigDao.insert(productIngredientConfig);

    }

    @Override
    @Transactional
    public void createAndSkuAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
        for (ProductOption productOption : productOptions) {
            productOption.setProductId(product.getId());
            productOptionDao.insert(productOption);
        }
    }

    @Override
    @Transactional
    public void createAndSkuAndIngredientAndConfigAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
        for (ProductOption productOption : productOptions) {
            productOption.setProductId(product.getId());
            productOptionDao.insert(productOption);
        }
        for (ProductIngredient productIngredient : productIngredients) {
            productIngredient.setProductId(product.getId());
            productIngredientDao.insert(productIngredient);
        }
        productIngredientConfig.setProductId(product.getId());
        productIngredientConfigDao.insert(productIngredientConfig);
    }


    @Override
    @Transactional
    public void modifyAndSku(Product product, List<ProductSku> productSkus) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
    }

    @Override
    @Transactional
    public void modifyAndSkuAndIngredientAndConfig(Product product, List<ProductSku> productSkus, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
        for (ProductIngredient productIngredient : productIngredients) {
            productIngredient.setProductId(product.getId());
            productIngredientDao.insert(productIngredient);
        }
        productIngredientConfig.setProductId(product.getId());
        productIngredientConfigDao.insert(productIngredientConfig);

    }

    @Override
    @Transactional
    public void modifyAndSkuAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions) {
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            productSku.setProductId(product.getId());
            productSkuDao.insert(productSku);
        }
        for (ProductOption productOption : productOptions) {
            productOption.setProductId(product.getId());
            productOptionDao.insert(productOption);
        }
    }

    @Override
    @Transactional
    public void modifyAndSkuAndIngredientAndConfigAndOption(Product product, List<ProductSku> productSkus, List<ProductOption> productOptions, List<ProductIngredient> productIngredients, ProductIngredientConfig productIngredientConfig) {
        // 查询这个产品下的SKU
        ProductSkuQuery productSkuQuery = new ProductSkuQuery();
        productSkuQuery.setProductId(product.getId());
        productSkuQuery.setStatus(DataStatus.Y.getCode());
        List<ProductSkuDTO> productSkuDTOList = productSkuDao.select(productSkuQuery);
        // 查询已有的
        List<Long> havingProductSkuIds = new ArrayList<>();
        for (ProductSkuDTO productSkuDTO : productSkuDTOList) {
            havingProductSkuIds.add(productSkuDTO.getId());
        }
        // 查询已有的产品小料配置
        ProductIngredientConfigQuery productIngredientConfigQuery = new ProductIngredientConfigQuery();
        productIngredientConfigQuery.setProductId(product.getId());
        productIngredientConfigQuery.setStatus(DataStatus.Y.getCode());
        List<ProductIngredientConfigDTO> productIngredientConfigDTOList = productIngredientConfigDao.select(productIngredientConfigQuery);
        List<Long> havingProductIngredientConfigIds = new ArrayList<>();
        for (ProductIngredientConfigDTO productIngredientConfigDTO : productIngredientConfigDTOList) {
            havingProductIngredientConfigIds.add(productIngredientConfigDTO.getId());
        }
        // 查询已有的产品小料
        ProductIngredientQuery productIngredientQuery = new ProductIngredientQuery();
        productIngredientQuery.setProductId(product.getId());
        productIngredientQuery.setStatus(DataStatus.Y.getCode());
        List<ProductIngredientDTO> productIngredientDTOList = productIngredientDao.select(productIngredientQuery);
        List<Long> havingProductIngredientIds = new ArrayList<>();
        for (ProductIngredientDTO productIngredientDTO : productIngredientDTOList) {
            havingProductIngredientIds.add(productIngredientDTO.getId());
        }
        // 查询已有的产品选项
        ProductOptionQuery productOptionQuery = new ProductOptionQuery();
        productOptionQuery.setProductId(product.getId());
        productOptionQuery.setStatus(DataStatus.Y.getCode());
        List<ProductOptionDTO> productOptionDTOList = productOptionDao.select(productOptionQuery);
        List<Long> havingProductOptionIds = new ArrayList<>();
        for (ProductOptionDTO productOptionDTO : productOptionDTOList) {
            havingProductOptionIds.add(productOptionDTO.getId());
        }
        productDao.insert(product);
        for (ProductSku productSku : productSkus) {
            if (productSku.getId() == null) {
                productSku.setProductId(product.getId());
                productSkuDao.insert(productSku);
            } else {
                havingProductSkuIds.remove(productSku.getId());
                productSkuDao.updateById(productSku);
            }
        }
        for (ProductOption productOption : productOptions) {
            if (productOption.getId() == null) {
                productOption.setProductId(product.getId());
                productOptionDao.insert(productOption);
            } else {
                havingProductOptionIds.remove(productOption.getId());
                productOptionDao.updateById(productOption);
            }
        }
        for (ProductIngredient productIngredient : productIngredients) {
            if (productIngredient.getId() == null) {
                productIngredient.setProductId(product.getId());
                productIngredientDao.insert(productIngredient);
            } else {
                havingProductIngredientIds.remove(productIngredient.getId());
                productIngredientDao.updateById(productIngredient);
            }
        }
        if (productIngredientConfig.getId() == null) {
            productIngredientConfig.setProductId(product.getId());
            productIngredientConfigDao.insert(productIngredientConfig);
        } else {
            havingProductIngredientConfigIds.remove(productIngredientConfig.getId());
            productIngredientConfigDao.updateById(productIngredientConfig);
        }
        // 将剩余的删除
        for (Long havingProductSkuId : havingProductSkuIds) {
            ProductSku productSku = new ProductSku();
            productSku.setId(havingProductSkuId);
            productSku.setStatus(DataStatus.N.getCode());
            productSkuDao.updateById(productSku);
        }
        for (Long havingProductOptionId : havingProductOptionIds) {
            ProductOption productOption = new ProductOption();
            productOption.setId(havingProductOptionId);
            productOption.setStatus(DataStatus.N.getCode());
            productOptionDao.updateById(productOption);
        }
        for (Long havingProductIngredientId : havingProductIngredientIds) {
            ProductIngredient productIngredient = new ProductIngredient();
            productIngredient.setId(havingProductIngredientId);
            productIngredient.setStatus(DataStatus.N.getCode());
            productIngredientDao.updateById(productIngredient);
        }
        for (Long havingProductIngredientConfigId : havingProductIngredientConfigIds) {
            ProductIngredientConfig productIngredientConfigModify = new ProductIngredientConfig();
            productIngredientConfigModify.setId(havingProductIngredientConfigId);
            productIngredientConfigModify.setStatus(DataStatus.N.getCode());
            productIngredientConfigDao.updateById(productIngredientConfigModify);
        }
    }

    /**
     * 修改产品
     *
     * @param product 产品
     * @return 结果
     */
    @Override
    public int modifyById(Product product) {
        return productDao.updateById(product);
    }


    /**
     * 删除产品信息
     *
     * @param id 产品ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productDao.deleteById(id);
    }

}
