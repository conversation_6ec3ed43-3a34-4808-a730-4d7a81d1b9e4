package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.TicketDao;
import com.domain.Ticket;
import com.dto.TicketDTO;
import com.query.TicketQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.TicketService;

/**
 * 门票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
@Service
public class TicketServiceImpl extends BaseService implements TicketService {

    @Autowired
    private TicketDao ticketDao;

    /**
     * 查询门票
     * 
     * @param id 门票ID
     * @return 门票
     */
    @Override
    public TicketDTO findById(Long id) {
        return ticketDao.selectById(id);
    }

    /**
     * 查询门票列表
     *
     * @param ids 编号集合
     * @return 门票集合
     */
    @Override
    public List<TicketDTO> findByIds(List<Long> ids) {
        return ticketDao.selectByIds(ids);
    }

    /**
     * 查询门票列表
     *
     * @param ticketQuery 门票
     * @return 门票
     */
    @Override
    public List<TicketDTO> findAll(TicketQuery ticketQuery) {
        return ticketDao.select(ticketQuery);
    }

	/**
	 * 分页查询门票列表
	 *
	 * @param ticketQuery 门票
	 * @return 门票
	 */
	@Override
	public PageInfo<TicketDTO> find(TicketQuery ticketQuery) {
        PageHelper.startPage(ticketQuery.getPageNum(),ticketQuery.getPageSize());
		List<TicketDTO> ticketDTOList = ticketDao.select(ticketQuery);
		return new PageInfo<>(ticketDTOList);
	}

    /**
     * 查询门票Map
     *
     * @param ids 编号集合
     * @return 门票Map
     */
    @Override
    public Map<Long, TicketDTO> findMapByIds(List<Long> ids) {
        Map<Long, TicketDTO> ticketDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<TicketDTO> ticketDTOList =  ticketDao.selectByIds(ids);
            for (TicketDTO ticketDTO : ticketDTOList) {
                    ticketDTOMap.put(ticketDTO.getId(),ticketDTO);
            }
        }
        return ticketDTOMap;
    }

    /**
     * 新增门票
     *
     * @param ticket 门票
     * @return 结果
     */
    @Override
    public int create(Ticket ticket) {
        return ticketDao.insert(ticket);
    }

    /**
     * 修改门票
     *
     * @param ticket 门票
     * @return 结果
     */
    @Override
    public int modifyById(Ticket ticket) {
        return ticketDao.updateById(ticket);
    }


    /**
     * 删除门票信息
     *
     * @param id 门票ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return ticketDao.deleteById(id);
    }

}
