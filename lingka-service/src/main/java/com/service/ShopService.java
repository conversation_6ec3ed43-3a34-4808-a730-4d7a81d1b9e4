package com.service;

import java.util.List;

import com.domain.Shop;
import com.domain.ShopConfig;
import com.dto.ShopDTO;
import com.github.pagehelper.PageInfo;
import com.query.ShopQuery;

public interface ShopService {
    /**
     * 查询店铺
     *
     * @param id 店铺主键
     * @return 店铺
     */
    ShopDTO selectShopById(Long id);

    /**
     * 查询店铺列表
     *
     * @param shopQuery 店铺
     * @return 店铺集合
     */
    PageInfo<ShopDTO> selectShopList(ShopQuery shopQuery);


    /**
     * 查询全部店铺列表
     *
     * @param shopQuery 店铺
     * @return 店铺集合
     */
    List<ShopDTO> findAll(ShopQuery shopQuery);

    /**
     * 新增店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    int insertShop(Shop shop);

    /**
     * 新增店铺和店铺配置
     *
     * @param shop 店铺
     * @return 结果
     */
    int createShopAndShopConfig(Shop shop, ShopConfig shopConfig);

    /**
     * 修改店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    int updateShop(Shop shop);

    /**
     * 批量删除店铺
     *
     * @param ids 需要删除的店铺主键集合
     * @return 结果
     */
    int deleteShopByIds(String ids);

    /**
     * 删除店铺信息
     *
     * @param id 店铺主键
     * @return 结果
     */
    int deleteShopById(Long id);
}
