package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserRole;
import com.github.pagehelper.PageInfo;
import com.domain.UserShop;
import com.dto.UserShopDTO;
import com.query.UserShopQuery;

/**
 * 用户店铺关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface UserShopService {
    /**
     * 查询用户店铺关联
     * 
     * @param id 用户店铺关联ID
     * @return 用户店铺关联
     */
    UserShopDTO findById(Long id);

    /**
     * 查询用户店铺关联列表
     *
     * @param ids 编号集合
     * @return 用户店铺关联集合
     */
    List<UserShopDTO> findByIds(List<Long> ids);

    /**
     * 查询用户店铺关联列表
     * 
     * @param userShopQuery 用户店铺关联
     * @return 用户店铺关联集合
     */
    List<UserShopDTO> findAll(UserShopQuery userShopQuery);

	/**
	 *  分页查询用户店铺关联列表
	 *
	 * @param userShopQuery 用户店铺关联
	 * @return 用户店铺关联集合
	 */
	PageInfo<UserShopDTO> find(UserShopQuery userShopQuery);

    /**
     * 查询用户店铺关联Map
     *
     * @param ids 编号集合
     * @return 用户店铺关联Map
     */
    Map<Long, UserShopDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户店铺关联
     * 
     * @param userShop 用户店铺关联
     * @return 结果
     */
    int create(UserShop userShop);

    /**
     * 新增用户店铺关联
     *
     * @param userShop 用户店铺关联
     * @return 结果
     */
    int createAndUserRoles(UserShop userShop, List<UserRole> userRoles);

    /**
     * 修改用户店铺关联
     * 
     * @param userShop 用户店铺关联
     * @return 结果
     */
    int modifyById(UserShop userShop);

    /**
     * 删除用户店铺关联信息
     * 
     * @param id 用户店铺关联id
     * @return 结果
     */
   int removeById(Long id);

    /**
     * 删除用户店铺关联信息
     *
     * @param id 用户店铺关联id
     * @return 结果
     */
    int removeById(Long id,List<Long> userRoleIds);
}
