package com.service;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.common.util.DateUtil;

public class BaseService {
	protected final String DATE_FORMAT = "yyyy-MM-dd";
	protected final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
	Date getServerTime(){
		return DateUtil.getServerTime();
	}

	Date getDatetime(String datetime,String format){
		if( datetime != null){
			return DateUtil.parse(datetime, format);
		}
		return null;
	}
	
}
