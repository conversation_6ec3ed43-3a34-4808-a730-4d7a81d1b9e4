package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.UserRole;
import com.dto.UserRoleDTO;
import com.query.UserRoleQuery;

/**
 * 用户角色关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface UserRoleService {
    /**
     * 查询用户角色关联
     * 
     * @param id 用户角色关联ID
     * @return 用户角色关联
     */
    UserRoleDTO findById(Long id);

    /**
     * 查询用户角色关联列表
     *
     * @param ids 编号集合
     * @return 用户角色关联集合
     */
    List<UserRoleDTO> findByIds(List<Long> ids);

    /**
     * 查询用户角色关联列表
     * 
     * @param userRoleQuery 用户角色关联
     * @return 用户角色关联集合
     */
    List<UserRoleDTO> findAll(UserRoleQuery userRoleQuery);

	/**
	 *  分页查询用户角色关联列表
	 *
	 * @param userRoleQuery 用户角色关联
	 * @return 用户角色关联集合
	 */
	PageInfo<UserRoleDTO> find(UserRoleQuery userRoleQuery);

    /**
     * 查询用户角色关联Map
     *
     * @param ids 编号集合
     * @return 用户角色关联Map
     */
    Map<Long, UserRoleDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户角色关联
     * 
     * @param userRole 用户角色关联
     * @return 结果
     */
    int create(UserRole userRole);

    /**
     * 修改用户角色关联
     * 
     * @param userRole 用户角色关联
     * @return 结果
     */
    int modifyById(UserRole userRole);

    /**
     * 删除用户角色关联信息
     * 
     * @param id 用户角色关联id
     * @return 结果
     */
   int removeById(Long id);

    /**
     * 重新绑定用户角色关联信息
     *
     * @return 结果
     */
    void removeByUserIdAndShopIdAndCreate(Long userId,Long shopId,List<Long> roleIds);
}
