package com.service;

import java.util.List;
import java.util.Map;

import com.domain.TicketProduct;
import com.github.pagehelper.PageInfo;
import com.domain.Ticket;
import com.dto.TicketDTO;
import com.query.TicketQuery;

/**
 * 门票Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface TicketService {
    /**
     * 查询门票
     * 
     * @param id 门票ID
     * @return 门票
     */
    TicketDTO findById(Long id);

    /**
     * 查询门票列表
     *
     * @param ids 编号集合
     * @return 门票集合
     */
    List<TicketDTO> findByIds(List<Long> ids);

    /**
     * 查询门票列表
     * 
     * @param ticketQuery 门票
     * @return 门票集合
     */
    List<TicketDTO> findAll(TicketQuery ticketQuery);

	/**
	 *  分页查询门票列表
	 *
	 * @param ticketQuery 门票
	 * @return 门票集合
	 */
	PageInfo<TicketDTO> find(TicketQuery ticketQuery);

    /**
     * 查询门票Map
     *
     * @param ids 编号集合
     * @return 门票Map
     */
    Map<Long, TicketDTO> findMapByIds(List<Long> ids);

    /**
     * 新增门票
     * 
     * @param ticket 门票
     * @return 结果
     */
    int create(Ticket ticket);

    /**
     * 新增门票
     *
     * @param ticket 门票
     * @param ticketProducts 门票产品
     * @return 结果
     */
    int create(Ticket ticket, List<TicketProduct> ticketProducts);

    /**
     * 修改门票
     * 
     * @param ticket 门票
     * @return 结果
     */
    int modifyById(Ticket ticket);

    /**
     * 删除门票信息
     * 
     * @param id 门票id
     * @return 结果
     */
   int removeById(Long id);
}
