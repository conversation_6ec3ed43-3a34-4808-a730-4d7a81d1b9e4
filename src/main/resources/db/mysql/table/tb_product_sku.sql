drop table if exists `tb_product_sku` ;
create table `tb_product_sku` (
    `id` bigint not null auto_increment comment '主键',
    `product_id` bigint comment '产品ID',
    `size` int comment '大小',
    `unit` varchar(20) comment '单位',
    `price` decimal(10,2) comment '价格',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品SKU表';
alter table `tb_product_sku` add index `idx_product_sku_01` (`product_id`);