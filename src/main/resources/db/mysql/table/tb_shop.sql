drop table if exists `tb_shop` ;
create table `tb_shop` (
    `id` bigint not null auto_increment comment '主键',
    `owner_user_id` bigint comment '拥有者用户ID',
    `owner_name` varchar(20) comment '店铺拥有者姓名',
    `owner_mobile` varchar(11) comment '店铺拥有者手机号',
    `owner_id_number` varchar(64) comment '店铺拥有者身份证号码',
    `owner_id_number_photo` varchar(64) comment '店铺拥有者身份证正反面照片',
    `responsibility_commitment` varchar(64) comment '责任承诺函文件',
    `manage_relationship` varchar(64) comment '管理关系声明文件',
    `business_license` varchar(64) comment '营业执照文件',
    `food_business_license` varchar(64) comment '食品经营许可证文件',
    `liquor_license` varchar(64) comment '酒类流通许可证文件',
    `health_certificates` varchar(5000) comment '健康证文件(多张用,分割)',
    `fire_license` varchar(64) comment '消防证文件',
    `create_admin_id` bigint comment '添加人',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '店铺表';
