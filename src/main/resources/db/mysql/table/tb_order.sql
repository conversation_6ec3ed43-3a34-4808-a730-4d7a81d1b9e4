drop table if exists `tb_order` ;
create table `tb_order` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `user_id` bigint comment '用户ID',
    `code` varchar(64) comment '订单号',
    `type` bigint comment '类型(ticket:门票 product:普通点单 diy:DIY)',
    `name` varchar(20) comment '名字',
    `amount` decimal(10,2) comment '金额',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单表';
alter table `tb_order` add index `idx_order_01` (`shop_id`);
alter table `tb_order` add index `idx_order_01` (`user_id`);
alter table `tb_order` add index `idx_order_01` (`code`);