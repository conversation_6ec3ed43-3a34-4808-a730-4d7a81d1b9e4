<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.TicketDao">
    
    <resultMap type="com.dto.TicketDTO" id="ticketResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="name"    column="name"    />
        <result property="priceCatalog"    column="price_catalog"    />
        <result property="price"    column="price"    />
        <result property="productNumber"    column="product_number"    />
        <result property="description"    column="description"    />
        <result property="stage"    column="stage"    />
        <result property="photo"    column="photo"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.TicketQuery" resultMap="ticketResult">
        select * from tb_ticket
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="priceCatalog != null  and priceCatalog != ''"> and price_catalog = #{priceCatalog}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="productNumber != null "> and product_number = #{productNumber}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="stage != null  and stage != ''"> and stage = #{stage}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="ticketResult">
         select * from tb_ticket where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="ticketResult">
        select * from tb_ticket where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.Ticket" useGeneratedKeys="true" keyProperty="id">
        insert into tb_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="name != null">name,</if>
            <if test="priceCatalog != null">price_catalog,</if>
            <if test="price != null">price,</if>
            <if test="productNumber != null">product_number,</if>
            <if test="description != null">description,</if>
            <if test="stage != null">stage,</if>
            <if test="photo != null">photo,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="name != null">#{name},</if>
            <if test="priceCatalog != null">#{priceCatalog},</if>
            <if test="price != null">#{price},</if>
            <if test="productNumber != null">#{productNumber},</if>
            <if test="description != null">#{description},</if>
            <if test="stage != null">#{stage},</if>
            <if test="photo != null">#{photo},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Ticket">
        update tb_ticket
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="priceCatalog != null">price_catalog = #{priceCatalog},</if>
            <if test="price != null">price = #{price},</if>
            <if test="productNumber != null">product_number = #{productNumber},</if>
            <if test="description != null">description = #{description},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_ticket where id = #{id}
    </delete>


</mapper>