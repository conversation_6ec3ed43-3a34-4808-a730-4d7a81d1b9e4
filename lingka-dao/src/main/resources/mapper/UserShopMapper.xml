<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserShopDao">
    
    <resultMap type="com.dto.UserShopDTO" id="userShopResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.UserShopQuery" resultMap="userShopResult">
        select * from tb_user_shop
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopIds != null and shopIds.size > 0">
                and shop_id in
                <foreach collection="shopIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="userShopResult">
         select * from tb_user_shop where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="userShopResult">
        select * from tb_user_shop where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.UserShop" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.UserShop">
        update tb_user_shop
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_user_shop where id = #{id}
    </delete>


</mapper>