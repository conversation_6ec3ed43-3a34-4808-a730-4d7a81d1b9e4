<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ProductIngredientConfigDao">
    
    <resultMap type="com.dto.ProductIngredientConfigDTO" id="productIngredientConfigResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="type"    column="type"    />
        <result property="required"    column="required"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.ProductIngredientConfigQuery" resultMap="productIngredientConfigResult">
        select * from tb_product_ingredient_config
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="required != null  and required != ''"> and required = #{required}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="productIngredientConfigResult">
         select * from tb_product_ingredient_config where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="productIngredientConfigResult">
        select * from tb_product_ingredient_config where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.ProductIngredientConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_product_ingredient_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="type != null">type,</if>
            <if test="required != null">required,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="type != null">#{type},</if>
            <if test="required != null">#{required},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ProductIngredientConfig">
        update tb_product_ingredient_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="required != null">required = #{required},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_product_ingredient_config where id = #{id}
    </delete>


</mapper>