<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ShopDao">
    
    <resultMap type="com.dto.ShopDTO" id="ShopResult">
        <result property="id"    column="id"    />
        <result property="ownerUserId"    column="owner_user_id"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="ownerMobile"    column="owner_mobile"    />
        <result property="ownerIdNumber"    column="owner_id_number"    />
        <result property="ownerIdNumberPhoto"    column="owner_id_number_photo"    />
        <result property="responsibilityCommitment"    column="responsibility_commitment"    />
        <result property="manageRelationship"    column="manage_relationship"    />
        <result property="businessLicense"    column="business_license"    />
        <result property="foodBusinessLicense"    column="food_business_license"    />
        <result property="liquorLicense"    column="liquor_license"    />
        <result property="healthCertificates"    column="health_certificates"    />
        <result property="fireLicense"    column="fire_license"    />
        <result property="createAdminId"    column="create_admin_id"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectShopVo">
        select id, owner_user_id, owner_name, owner_mobile, owner_id_number, owner_id_number_photo, responsibility_commitment, manage_relationship, business_license, food_business_license, liquor_license, health_certificates, fire_license, create_admin_id, status, modify_time, create_time from tb_shop
    </sql>

    <select id="selectShopList" parameterType="com.query.ShopQuery" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        <where>  
            <if test="shopName != null and shopName != ''"> and id in(
             select shop_id from tb_shop_config
                <where>
                    <if test="shopName != null and shopName != ''">
                        name like concat('%', #{shopName}, '%')
                    </if>
                </where>
                )
                </if>
            <if test="ownerUserId != null "> and owner_user_id = #{ownerUserId}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="ownerMobile != null  and ownerMobile != ''"> and owner_mobile = #{ownerMobile}</if>
            <if test="ownerIdNumber != null  and ownerIdNumber != ''"> and owner_id_number = #{ownerIdNumber}</if>
            <if test="ownerIdNumberPhoto != null  and ownerIdNumberPhoto != ''"> and owner_id_number_photo = #{ownerIdNumberPhoto}</if>
            <if test="responsibilityCommitment != null  and responsibilityCommitment != ''"> and responsibility_commitment = #{responsibilityCommitment}</if>
            <if test="manageRelationship != null  and manageRelationship != ''"> and manage_relationship = #{manageRelationship}</if>
            <if test="businessLicense != null  and businessLicense != ''"> and business_license = #{businessLicense}</if>
            <if test="foodBusinessLicense != null  and foodBusinessLicense != ''"> and food_business_license = #{foodBusinessLicense}</if>
            <if test="liquorLicense != null  and liquorLicense != ''"> and liquor_license = #{liquorLicense}</if>
            <if test="healthCertificates != null  and healthCertificates != ''"> and health_certificates = #{healthCertificates}</if>
            <if test="fireLicense != null  and fireLicense != ''"> and fire_license = #{fireLicense}</if>
            <if test="createAdminId != null "> and create_admin_id = #{createAdminId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectShopById" parameterType="Long" resultMap="ShopResult">
        <include refid="selectShopVo"/>
        where id = #{id}
    </select>

    <insert id="insertShop" parameterType="com.domain.Shop" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ownerUserId != null">owner_user_id,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="ownerMobile != null">owner_mobile,</if>
            <if test="ownerIdNumber != null">owner_id_number,</if>
            <if test="ownerIdNumberPhoto != null">owner_id_number_photo,</if>
            <if test="responsibilityCommitment != null">responsibility_commitment,</if>
            <if test="manageRelationship != null">manage_relationship,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="foodBusinessLicense != null">food_business_license,</if>
            <if test="liquorLicense != null">liquor_license,</if>
            <if test="healthCertificates != null">health_certificates,</if>
            <if test="fireLicense != null">fire_license,</if>
            <if test="createAdminId != null">create_admin_id,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ownerUserId != null">#{ownerUserId},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="ownerMobile != null">#{ownerMobile},</if>
            <if test="ownerIdNumber != null">#{ownerIdNumber},</if>
            <if test="ownerIdNumberPhoto != null">#{ownerIdNumberPhoto},</if>
            <if test="responsibilityCommitment != null">#{responsibilityCommitment},</if>
            <if test="manageRelationship != null">#{manageRelationship},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="foodBusinessLicense != null">#{foodBusinessLicense},</if>
            <if test="liquorLicense != null">#{liquorLicense},</if>
            <if test="healthCertificates != null">#{healthCertificates},</if>
            <if test="fireLicense != null">#{fireLicense},</if>
            <if test="createAdminId != null">#{createAdminId},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateShop" parameterType="com.domain.Shop">
        update tb_shop
        <trim prefix="SET" suffixOverrides=",">
            <if test="ownerUserId != null">owner_user_id = #{ownerUserId},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="ownerMobile != null">owner_mobile = #{ownerMobile},</if>
            <if test="ownerIdNumber != null">owner_id_number = #{ownerIdNumber},</if>
            <if test="ownerIdNumberPhoto != null">owner_id_number_photo = #{ownerIdNumberPhoto},</if>
            <if test="responsibilityCommitment != null">responsibility_commitment = #{responsibilityCommitment},</if>
            <if test="manageRelationship != null">manage_relationship = #{manageRelationship},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="foodBusinessLicense != null">food_business_license = #{foodBusinessLicense},</if>
            <if test="liquorLicense != null">liquor_license = #{liquorLicense},</if>
            <if test="healthCertificates != null">health_certificates = #{healthCertificates},</if>
            <if test="fireLicense != null">fire_license = #{fireLicense},</if>
            <if test="createAdminId != null">create_admin_id = #{createAdminId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShopById" parameterType="Long">
        delete from tb_shop where id = #{id}
    </delete>

    <delete id="deleteShopByIds" parameterType="String">
        delete from tb_shop where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>