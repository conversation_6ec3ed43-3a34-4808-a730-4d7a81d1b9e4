<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.TicketProductDao">
    
    <resultMap type="com.dto.TicketProductDTO" id="ticketProductResult">
        <result property="id"    column="id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productNumber"    column="product_number"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.TicketProductQuery" resultMap="ticketProductResult">
        select * from tb_ticket_product
        <where>  
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productNumber != null "> and product_number = #{productNumber}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="ticketProductResult">
         select * from tb_ticket_product where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="ticketProductResult">
        select * from tb_ticket_product where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.TicketProduct" useGeneratedKeys="true" keyProperty="id">
        insert into tb_ticket_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ticketId != null">ticket_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productNumber != null">product_number,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ticketId != null">#{ticketId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productNumber != null">#{productNumber},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.TicketProduct">
        update tb_ticket_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productNumber != null">product_number = #{productNumber},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_ticket_product where id = #{id}
    </delete>


</mapper>