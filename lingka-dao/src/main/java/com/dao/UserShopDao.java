package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserShop;
import com.dto.UserShopDTO;
import com.query.UserShopQuery;

/**
 * 用户店铺关联 接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */

@Mapper
public interface UserShopDao {
    /**
     * 查询用户店铺关联
     * 
     * @param id 用户店铺关联id
     * @return 用户店铺关联
     */
    UserShopDTO selectById(Long id);


    /**
     * 查询用户店铺关联列表
     *
     * @param ids 编号集合
     * @return 用户店铺关联集合
     */
    List<UserShopDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户店铺关联列表
     * 
     * @param userShopQuery 用户店铺关联
     * @return 用户店铺关联集合
     */
    List<UserShopDTO> select(UserShopQuery userShopQuery);

    /**
     * 新增用户店铺关联
     * 
     * @param userShop 用户店铺关联
     * @return 结果
     */
    int insert(UserShop userShop);

    /**
     * 修改用户店铺关联
     * 
     * @param userShop 用户店铺关联
     * @return 结果
     */
    int updateById(UserShop userShop);

    /**
     * 删除用户店铺关联
     * 
     * @param id 用户店铺关联Id
     * @return 结果
     */
    int deleteById(Long id);

}
