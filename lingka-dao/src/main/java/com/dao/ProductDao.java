package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Product;
import com.dto.ProductDTO;
import com.query.ProductQuery;

/**
 * 产品 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductDao {
    /**
     * 查询产品
     * 
     * @param id 产品id
     * @return 产品
     */
    ProductDTO selectById(Long id);


    /**
     * 查询产品列表
     *
     * @param ids 编号集合
     * @return 产品集合
     */
    List<ProductDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品列表
     * 
     * @param productQuery 产品
     * @return 产品集合
     */
    List<ProductDTO> select(ProductQuery productQuery);

    /**
     * 新增产品
     * 
     * @param product 产品
     * @return 结果
     */
    int insert(Product product);

    /**
     * 修改产品
     * 
     * @param product 产品
     * @return 结果
     */
    int updateById(Product product);

    /**
     * 删除产品
     * 
     * @param id 产品Id
     * @return 结果
     */
    int deleteById(Long id);

}
