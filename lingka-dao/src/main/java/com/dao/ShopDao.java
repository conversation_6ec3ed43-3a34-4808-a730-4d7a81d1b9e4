package com.dao;

import java.util.List;
import com.domain.Shop;
import com.dto.ShopDTO;
import com.query.ShopQuery;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ShopDao {
    /**
     * 查询店铺
     *
     * @param id 店铺主键
     * @return 店铺
     */
    ShopDTO selectShopById(Long id);

    /**
     * 查询店铺列表
     *
     * @param shopQuery 店铺
     * @return 店铺集合
     */
    List<ShopDTO> selectShopList(ShopQuery shopQuery);

    /**
     * 新增店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    int insertShop(Shop shop);

    /**
     * 修改店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    int updateShop(Shop shop);

    /**
     * 删除店铺
     *
     * @param id 店铺主键
     * @return 结果
     */
    int deleteShopById(Long id);

    /**
     * 批量删除店铺
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteShopByIds(String[] ids);
}
