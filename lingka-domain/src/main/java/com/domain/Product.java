package com.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 产品对象 tb_product
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

public class Product extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 分类ID */
    private Long catalogId;

    /** 产品名字 */
    private String name;

    /** 群酒(0:是 1:否) */
    private String tableFlag;

    /** 产品介绍 */
    private String description;

    /** 上下架状态(up:上架 down:下架) */
    private String stage;

    /** 图片 */
    private String photo;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setCatalogId(Long catalogId) {
        this.catalogId = catalogId;
    }

    public Long getCatalogId() {
        return catalogId;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public void setTableFlag(String tableFlag) {
        this.tableFlag = tableFlag;
    }

    public String getTableFlag() {
        return tableFlag;
    }
    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getStage() {
        return stage;
    }
    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhoto() {
        return photo;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
