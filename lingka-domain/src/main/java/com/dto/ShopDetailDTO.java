package com.dto;

import com.common.bean.Bean;

public class ShopDetailDTO extends Bean {
    private static final long serialVersionUID = -6258165107943754575L;
    /**
     * 店铺 ID
     */
    private Long id;

    /**
     * 拥有者用户ID
     */
    private Long ownerUserId;

    /**
     * 店铺拥有者姓名
     */
    private String ownerName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺拥有者手机号
     */
    private String ownerMobile;

    /**
     * 店铺拥有者身份证号码
     */
    private String ownerIdNumber;

    /**
     * 店铺拥有者身份证正反面照片
     */
    private String ownerIdNumberPhoto;

    /**
     * 责任承诺函文件
     */
    private String responsibilityCommitment;

    /**
     * 管理关系声明文件
     */
    private String manageRelationship;

    /**
     * 营业执照文件
     */
    private String businessLicense;

    /**
     * 食品经营许可证文件
     */
    private String foodBusinessLicense;

    /**
     * 酒类流通许可证文件
     */
    private String liquorLicense;

    /**
     * 健康证文件(多张用,分割)
     */
    private String healthCertificates;

    /**
     * 消防证文件
     */
    private String fireLicense;

    /**
     * 添加人
     */
    private Long createAdminId;

    /**
     * 添加人姓名
     */
    private String createAdminName;

    /**
     * 店铺配置 ID
     */
    private Long config_id;

    /**
     * 店铺 ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺联系方式
     */
    private String phone;

    /**
     * 店铺介绍头图文件
     */
    private String[] photo;

    /**
     * 店铺介绍描述
     */
    private String description;

    /**
     * 店铺标签
     */
    private String[] tags;

    /**
     * 是否对外展示 (0: 展示 1: 隐藏)
     */
    private String displaySwitch;

    /**
     * 营业状态(open:营业 close:暂停营业 close_manual:暂停营业(手动))
     */
    private String state;

    /**
     * 省编号
     */
    private Long provinceId;

    /**
     * 市编号
     */
    private Long cityId;

    /**
     * 区编号
     */
    private Long distinctId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    public ShopDetailDTO(ShopDTO shopDTO, ShopConfigDTO configDTO) {
        // ShopDTO 字段
        this.id = shopDTO.getId();
        this.ownerUserId = shopDTO.getOwnerUserId();
        this.ownerName = shopDTO.getOwnerName();
        this.shopName = shopDTO.getShopName();
        this.ownerMobile = shopDTO.getOwnerMobile();
        this.ownerIdNumber = shopDTO.getOwnerIdNumber();
        this.ownerIdNumberPhoto = shopDTO.getOwnerIdNumberPhoto();
        this.responsibilityCommitment = shopDTO.getResponsibilityCommitment();
        this.manageRelationship = shopDTO.getManageRelationship();
        this.businessLicense = shopDTO.getBusinessLicense();
        this.foodBusinessLicense = shopDTO.getFoodBusinessLicense();
        this.liquorLicense = shopDTO.getLiquorLicense();
        this.healthCertificates = shopDTO.getHealthCertificates();
        this.fireLicense = shopDTO.getFireLicense();
        this.createAdminId = shopDTO.getCreateAdminId();
        this.createAdminName = shopDTO.getCreateAdminName();

        // ShopConfigDTO 字段
        this.config_id = configDTO.getId();
        this.shopId = configDTO.getShopId();
        this.name = configDTO.getName();
        this.logo = configDTO.getLogo();
        this.phone = configDTO.getPhone();
        this.photo = configDTO.getPhoto();
        this.description = configDTO.getDescription();
        this.tags = configDTO.getTags();
        this.displaySwitch = configDTO.getDisplaySwitch();
        this.state = configDTO.getState();
        this.provinceId = configDTO.getProvinceId();
        this.cityId = configDTO.getCityId();
        this.distinctId = configDTO.getDistinctId();
        this.address = configDTO.getAddress();
        this.latitude = configDTO.getLatitude();
        this.longitude = configDTO.getLongitude();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getOwnerMobile() {
        return ownerMobile;
    }

    public void setOwnerMobile(String ownerMobile) {
        this.ownerMobile = ownerMobile;
    }

    public String getOwnerIdNumber() {
        return ownerIdNumber;
    }

    public void setOwnerIdNumber(String ownerIdNumber) {
        this.ownerIdNumber = ownerIdNumber;
    }

    public String getOwnerIdNumberPhoto() {
        return ownerIdNumberPhoto;
    }

    public void setOwnerIdNumberPhoto(String ownerIdNumberPhoto) {
        this.ownerIdNumberPhoto = ownerIdNumberPhoto;
    }

    public String getResponsibilityCommitment() {
        return responsibilityCommitment;
    }

    public void setResponsibilityCommitment(String responsibilityCommitment) {
        this.responsibilityCommitment = responsibilityCommitment;
    }

    public String getManageRelationship() {
        return manageRelationship;
    }

    public void setManageRelationship(String manageRelationship) {
        this.manageRelationship = manageRelationship;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getFoodBusinessLicense() {
        return foodBusinessLicense;
    }

    public void setFoodBusinessLicense(String foodBusinessLicense) {
        this.foodBusinessLicense = foodBusinessLicense;
    }

    public String getLiquorLicense() {
        return liquorLicense;
    }

    public void setLiquorLicense(String liquorLicense) {
        this.liquorLicense = liquorLicense;
    }

    public String getHealthCertificates() {
        return healthCertificates;
    }

    public void setHealthCertificates(String healthCertificates) {
        this.healthCertificates = healthCertificates;
    }

    public String getFireLicense() {
        return fireLicense;
    }

    public void setFireLicense(String fireLicense) {
        this.fireLicense = fireLicense;
    }

    public Long getCreateAdminId() {
        return createAdminId;
    }

    public void setCreateAdminId(Long createAdminId) {
        this.createAdminId = createAdminId;
    }

    public String getCreateAdminName() {
        return createAdminName;
    }

    public void setCreateAdminName(String createAdminName) {
        this.createAdminName = createAdminName;
    }

    public Long getConfig_id() {
        return config_id;
    }

    public void setConfig_id(Long config_id) {
        this.config_id = config_id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String[] getPhoto() {
        return photo;
    }

    public void setPhoto(String[] photo) {
        this.photo = photo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String[] getTags() {
        return tags;
    }

    public void setTags(String[] tags) {
        this.tags = tags;
    }

    public String getDisplaySwitch() {
        return displaySwitch;
    }

    public void setDisplaySwitch(String displaySwitch) {
        this.displaySwitch = displaySwitch;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getDistinctId() {
        return distinctId;
    }

    public void setDistinctId(Long distinctId) {
        this.distinctId = distinctId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
}
